#!/usr/bin/env python3
"""
Test script to validate Dify integration and basic functionality.
Run this script to ensure the refactored system works correctly.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config import get_settings
from core.db.database import AsyncSessionLocal
from core.services.dify_service import dify_service
from modules.call import crud as call_crud
from modules.customers import crud as customer_crud


async def test_configuration():
    """Test that all required configuration is present."""
    print("🔧 Testing Configuration...")

    try:
        settings = get_settings()

        # Check required settings
        required_settings = [
            "DATABASE_URL",
            "SECRET_KEY",
            "DIFY_API_KEY",
            "DIFY_BASE_URL",
        ]

        missing_settings = []
        for setting in required_settings:
            if not hasattr(settings, setting) or not getattr(settings, setting):
                missing_settings.append(setting)

        if missing_settings:
            print(f"❌ Missing required settings: {', '.join(missing_settings)}")
            return False

        print("✅ Configuration looks good!")
        return True

    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


async def test_database_connection():
    """Test database connectivity and basic operations."""
    print("🗄️  Testing Database Connection...")

    try:
        async with AsyncSessionLocal() as db:
            # Test customer operations (preserved business logic)
            customers = await customer_crud.get_customers(
                db, user_id=1, skip=0, limit=10
            )
            print(
                f"✅ Database connection successful! Found {len(customers)} customers."
            )
            return True

    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


async def test_dify_service():
    """Test Dify service initialization and basic functionality."""
    print("🤖 Testing Dify Service...")

    try:
        # Test service initialization
        service = dify_service
        print("✅ Dify service initialized successfully!")

        # Note: We can't test actual API calls without valid credentials
        # This just tests that the service can be imported and initialized
        print(
            "ℹ️  To test actual Dify API calls, ensure DIFY_API_KEY is configured correctly."
        )
        return True

    except Exception as e:
        print(f"❌ Dify service initialization failed: {e}")
        return False


async def test_conversation_manager():
    """Test conversation manager initialization."""
    print("💬 Testing Conversation Manager...")

    try:
        from core.services.conversation_manager import ConversationManager

        # Test initialization
        conversation_manager = ConversationManager(db_session_factory=AsyncSessionLocal)
        print("✅ Conversation manager initialized successfully!")
        return True

    except Exception as e:
        print(f"❌ Conversation manager initialization failed: {e}")
        return False


async def test_removed_components():
    """Test that removed components are actually removed."""
    print("🗑️  Testing Removed Components...")

    removed_imports = [
                "core.db.database",
        "modules.agents.models",
        "modules.agents.service",
    ]

    successfully_removed = []
    still_present = []

    for import_path in removed_imports:
        try:
            __import__(import_path)
            still_present.append(import_path)
        except ImportError:
            successfully_removed.append(import_path)

    if still_present:
        print(f"⚠️  Some components still present: {', '.join(still_present)}")
        print("   This might be okay if they're still needed for other functionality.")

    print(f"✅ Successfully removed: {len(successfully_removed)} components")
    return True


async def main():
    """Run all tests."""
    print("🚀 Starting Dify Integration Tests...\n")

    tests = [
        ("Configuration", test_configuration),
        ("Database Connection", test_database_connection),
        ("Dify Service", test_dify_service),
        ("Conversation Manager", test_conversation_manager),
        ("Removed Components", test_removed_components),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
        print()  # Add spacing between tests

    # Summary
    print("📊 Test Summary:")
    print("=" * 50)

    passed = 0
    failed = 0

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
        else:
            failed += 1

    print("=" * 50)
    print(f"Total: {len(results)} tests, {passed} passed, {failed} failed")

    if failed == 0:
        print("\n🎉 All tests passed! Dify integration looks good.")
        return 0
    else:
        print(
            f"\n⚠️  {failed} test(s) failed. Please check the configuration and setup."
        )
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
