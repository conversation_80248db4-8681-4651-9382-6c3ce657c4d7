#!/usr/bin/env python3
"""
Simple script to run the database seeding
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from seed_database import main

if __name__ == "__main__":
    print("🌱 Running database seeding script...")
    try:
        asyncio.run(main())
        print("✅ Database seeding completed successfully!")
    except Exception as e:
        print(f"❌ Database seeding failed: {e}")
        sys.exit(1)
