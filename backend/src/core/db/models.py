# This file is used to import all SQLAlchemy models to ensure they are registered
# with the Base.metadata object. This helps prevent issues with relationships
# not being properly configured during application startup.

from modules.agents import models as agents_models
from modules.analytics import models as analytics_models
from modules.api_docs import models as api_docs_models
from modules.bookings import models as bookings_models
from modules.call import models as call_models
from modules.chat_plugin import models as chat_plugin_models
from modules.companies import models as companies_models
from modules.conversations import models as conversations_models
from modules.customers import models as customers_models
from modules.jobs import models as jobs_models
from modules.knowledge_base import models as knowledge_base_models
from modules.performance import models as performance_models
from modules.phone_numbers import models as phone_numbers_models
from modules.requests import models as requests_models
from modules.testing import models as testing_models
from modules.users import models as users_models
from modules.web_forms import models as web_forms_models
