"""
Agent Service Layer
Business logic for agent operations, workflow management, and testing.
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from modules.call import models as call_models

from modules.templates import crud as template_crud

from . import crud, schemas

# ============================================================================
# AGENT MANAGEMENT
# ============================================================================


async def get_agents(
    db: AsyncSession,
    user_id: int,
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
) -> List[schemas.Agent]:
    """Get all agents with optional filtering."""
    agents = await crud.get_agents(
        db, skip=skip, limit=limit, category=category, user_id=user_id
    )

    # Convert to response format with statistics
    agent_responses = []
    for agent in agents:
        # Get conversation statistics
        stats = await _get_agent_statistics(db, agent.id, user_id)

        # Safely handle workflow conversion
        workflow_data = None
        if agent.workflow:
            try:
                workflow_data = schemas.WorkflowData(**agent.workflow)
            except Exception as e:
                # If workflow data is invalid, set to None
                workflow_data = None

        agent_response = schemas.Agent(
            id=agent.id,
            name=agent.name,
            description=agent.description,
            category=agent.category,
            is_active=agent.is_active,
            tags=agent.workflow.get("tags", []) if agent.workflow else [],
            workflow=workflow_data,
            created_at=agent.created_at,
            updated_at=agent.updated_at,
            created_by=agent.created_by,
            **stats,
        )
        agent_responses.append(agent_response)

    return agent_responses


async def get_agent(
    db: AsyncSession, agent_id: int, user_id: int
) -> Optional[schemas.Agent]:
    """Get a specific agent by ID."""
    agent = await crud.get_agent(db, agent_id, user_id)
    if not agent:
        return None

    stats = await _get_agent_statistics(db, agent_id, user_id)

    # Safely handle workflow conversion
    workflow_data = None
    if agent.workflow:
        try:
            workflow_data = schemas.WorkflowData(**agent.workflow)
        except Exception as e:
            # If workflow data is invalid, set to None
            workflow_data = None

    return schemas.Agent(
        id=agent.id,
        name=agent.name,
        description=agent.description,
        category=agent.category,
        is_active=agent.is_active,
        tags=agent.workflow.get("tags", []) if agent.workflow else [],
        workflow=workflow_data,
        created_at=agent.created_at,
        updated_at=agent.updated_at,
        created_by=agent.created_by,
        **stats,
    )


async def create_agent(
    db: AsyncSession, agent_data: schemas.AgentCreate, user_id: int
) -> schemas.Agent:
    """Create a new agent."""
    # Convert workflow to dict
    workflow_dict = agent_data.workflow.model_dump() if agent_data.workflow else {}

    # Create agent using CRUD
    agent = await crud.create_agent(db, agent_data, user_id)

    return schemas.Agent(
        id=agent.id,
        name=agent.name,
        description=agent.description,
        category=agent.category,
        is_active=agent.is_active,
        tags=agent_data.tags or [],
        workflow=agent_data.workflow,
        created_at=agent.created_at,
        updated_at=agent.updated_at,
        created_by=agent.created_by,
        total_conversations=0,
        success_rate=0.0,
        avg_response_time=0.0,
    )


async def update_agent(
    db: AsyncSession, agent_id: int, agent_data: schemas.AgentUpdate, user_id: int
) -> schemas.Agent:
    """Update an existing agent."""
    # Update agent using CRUD
    updated_agent = await crud.update_agent(db, agent_id, agent_data, user_id)

    # Get statistics
    stats = await _get_agent_statistics(db, agent_id, user_id)

    # Safely handle workflow conversion
    workflow_data = None
    if updated_agent.workflow:
        try:
            workflow_data = schemas.WorkflowData(**updated_agent.workflow)
        except Exception as e:
            # If workflow data is invalid, set to None
            workflow_data = None
    elif agent_data.workflow:
        workflow_data = agent_data.workflow

    return schemas.Agent(
        id=updated_agent.id,
        name=updated_agent.name,
        description=updated_agent.description,
        category=updated_agent.category,
        is_active=updated_agent.is_active,
        tags=updated_agent.workflow.get("tags", [])
        if updated_agent.workflow
        else (agent_data.tags or []),
        workflow=workflow_data,
        created_at=updated_agent.created_at,
        updated_at=updated_agent.updated_at,
        created_by=updated_agent.created_by,
        **stats,
    )


async def delete_agent(db: AsyncSession, agent_id: int, user_id: int):
    """Delete an agent."""
    await crud.delete_agent(db, agent_id, user_id)


async def duplicate_agent(
    db: AsyncSession, agent_id: int, name: str, user_id: int
) -> schemas.Agent:
    """Duplicate an existing agent."""
    # Get original agent
    original_agent = await crud.get_agent(db, agent_id)
    if not original_agent:
        raise ValueError("Agent not found")

    # Create duplicate data
    duplicate_data = schemas.AgentCreate(
        name=name,
        description=f"Copy of {original_agent.description}"
        if original_agent.description
        else None,
        category=original_agent.category,
        workflow=schemas.WorkflowData(**original_agent.workflow)
        if original_agent.workflow
        else None,
        is_active=False,  # Start as inactive
        tags=original_agent.workflow.get("tags", []) if original_agent.workflow else [],
    )

    return await create_agent(db, duplicate_data, user_id)


# ============================================================================
# WORKFLOW MANAGEMENT
# ============================================================================


async def validate_workflow(
    workflow: schemas.WorkflowData,
) -> schemas.WorkflowValidationResult:
    """Validate a workflow configuration."""
    errors = []
    warnings = []
    suggestions = []

    # Basic validation
    if not workflow.nodes:
        errors.append("Workflow must have at least one node")

    if not workflow.start_node_id:
        errors.append("Workflow must have a start node ID")

    # Check if start node exists
    start_node_exists = any(
        node.id == workflow.start_node_id for node in workflow.nodes
    )
    if workflow.start_node_id and not start_node_exists:
        errors.append(f"Start node '{workflow.start_node_id}' not found in workflow")

    # Validate connections
    connection_count = 0
    for source_id, connections in workflow.connections.items():
        if connections.main:
            connection_count += len(connections.main)
        if connections.conditional:
            for path_connections in connections.conditional.values():
                connection_count += len(path_connections)

    # Check for unreachable nodes
    reachable_nodes = set()
    if workflow.start_node_id:
        _find_reachable_nodes(
            workflow.start_node_id, workflow.connections, reachable_nodes
        )

    unreachable_nodes = [
        node.id
        for node in workflow.nodes
        if node.id not in reachable_nodes and node.id != workflow.start_node_id
    ]
    if unreachable_nodes:
        warnings.append(f"Unreachable nodes found: {', '.join(unreachable_nodes)}")

    # Estimate complexity
    complexity = "simple"
    if len(workflow.nodes) > 10:
        complexity = "moderate"
    if len(workflow.nodes) > 20:
        complexity = "complex"

    return schemas.WorkflowValidationResult(
        is_valid=len(errors) == 0,
        errors=errors,
        warnings=warnings,
        suggestions=suggestions,
        node_count=len(workflow.nodes),
        connection_count=connection_count,
        estimated_complexity=complexity,
    )


async def export_workflow(
    workflow: schemas.WorkflowData, format: str = "json"
) -> Dict[str, Any]:
    """Export workflow in specified format."""
    if format.lower() == "json":
        return {
            "format": "json",
            "data": workflow.model_dump(),
            "exported_at": datetime.utcnow().isoformat(),
            "version": "1.0.0",
        }
    else:
        raise ValueError(f"Unsupported format: {format}")


async def import_workflow(workflow_data: Dict[str, Any]) -> Dict[str, Any]:
    """Import workflow from data."""
    try:
        # Create workflow from imported data
        workflow = schemas.WorkflowData(**workflow_data.get("data", workflow_data))

        # Validate the imported workflow
        validation_result = await validate_workflow(workflow)

        return {
            "workflow": workflow.model_dump(),
            "validation": validation_result.model_dump(),
            "imported_at": datetime.utcnow().isoformat(),
        }
    except Exception as e:
        raise ValueError(f"Import failed: {str(e)}")


async def validate_node(
    node_config: schemas.NodeConfig,
) -> schemas.NodeValidationResult:
    """Validate a node configuration."""
    errors = []
    warnings = []

    # Basic validation
    if not node_config.name:
        errors.append("Node name is required")

    if not node_config.type:
        errors.append("Node type is required")

    # Type-specific validation
    if node_config.type == schemas.NodeType.LLM:
        if not node_config.system_prompt:
            warnings.append("LLM node should have a system prompt")

    elif node_config.type == schemas.NodeType.PYTHON_FUNCTION:
        if not node_config.function_path:
            errors.append("Python function node must have a function path")

    elif node_config.type == schemas.NodeType.CONDITIONAL:
        if not node_config.condition_type:
            errors.append("Conditional node must have a condition type")

    return schemas.NodeValidationResult(
        is_valid=len(errors) == 0, errors=errors, warnings=warnings
    )


# ============================================================================
# PLACEHOLDER METHODS (TO BE IMPLEMENTED)
# ============================================================================


async def execute_agent(
    db: AsyncSession, agent_id: int, execution_request: schemas.ExecutionRequest
) -> schemas.ExecutionResponse:
    """Execute an agent workflow."""
    execution_id = str(uuid.uuid4())
    return schemas.ExecutionResponse(
        execution_id=execution_id,
        status=schemas.ExecutionStatus.PENDING,
        started_at=datetime.now(),
    )


async def get_execution_status(execution_id: str) -> schemas.ExecutionStatusResponse:
    """Get execution status and logs."""
    return schemas.ExecutionStatusResponse(
        execution_id=execution_id,
        status=schemas.ExecutionStatus.COMPLETED,
        started_at=datetime.now(),
        completed_at=datetime.now(),
        progress=100.0,
    )


async def get_execution_logs(execution_id: str) -> List[schemas.ExecutionLog]:
    """Get detailed execution logs."""
    return []


async def test_agent(
    db: AsyncSession, agent_id: int, test_request: schemas.AgentTestRequest
) -> schemas.AgentTestResult:
    """Test an agent with sample input."""
    return schemas.AgentTestResult(
        success=True,
        response="Test response from agent",
        execution_trace=[],
        total_time_ms=150.0,
        channel_adapted=False,
    )


async def get_agent_analytics(
    db: AsyncSession, agent_id: int, time_range: str
) -> schemas.AgentAnalytics:
    """Get agent performance analytics."""
    return schemas.AgentAnalytics(
        agent_id=agent_id,
        time_range=time_range,
        total_conversations=0,
        successful_conversations=0,
        failed_conversations=0,
        success_rate=0.0,
        avg_response_time_ms=0.0,
        avg_conversation_duration_seconds=0.0,
        channel_metrics={},
        node_performance=[],
        daily_trends=[],
    )


async def get_agent_conversations(
    db: AsyncSession, agent_id: int, skip: int, limit: int
) -> List[schemas.ConversationSummary]:
    """Get agent conversation history."""
    return []


async def get_llm_providers() -> List[schemas.LLMProvider]:
    """Get available LLM providers."""
    return []


async def get_integrations() -> List[schemas.Integration]:
    """Get available integrations."""
    return []


async def get_agent_phone_numbers(
    db: AsyncSession, agent_id: int
) -> List[schemas.AssignedPhoneNumber]:
    """Get phone numbers assigned to an agent."""
    return []


async def assign_phone_number(
    db: AsyncSession, agent_id: int, phone_number_id: int, user_id: int
):
    """Assign a phone number to an agent."""
    pass


async def unassign_phone_number(
    db: AsyncSession, agent_id: int, phone_number_id: int, user_id: int
):
    """Unassign a phone number from an agent."""
    pass


async def get_transfer_rules(
    db: AsyncSession, agent_id: int
) -> List[schemas.TransferRule]:
    """Get transfer rules for an agent."""
    return []


async def create_transfer_rule(
    db: AsyncSession, agent_id: int, rule_data: schemas.TransferRuleCreate, user_id: int
) -> schemas.TransferRule:
    """Create a transfer rule for an agent."""
    pass


async def update_transfer_rule(
    db: AsyncSession, rule_id: int, rule_data: schemas.TransferRuleUpdate, user_id: int
) -> schemas.TransferRule:
    """Update a transfer rule."""
    pass


async def delete_transfer_rule(db: AsyncSession, rule_id: int, user_id: int):
    """Delete a transfer rule."""
    pass


# ============================================================================
# TEMPLATE INTEGRATION
# ============================================================================


async def create_agent_from_template(
    db: AsyncSession,
    template_id: str,
    agent_data: schemas.AgentFromTemplateRequest,
    user_id: int,
) -> schemas.Agent:
    """Create an agent from a template."""
    try:
        # Get the template from the templates module
        template = await template_crud.get_template(db, template_id, user_id)
        if not template:
            raise ValueError("Template not found")

        # Convert template workflow to our schema format
        workflow_data = None
        if template.workflow:
            try:
                workflow_data = schemas.WorkflowData(**template.workflow)
            except Exception:
                # If workflow conversion fails, create a basic workflow
                workflow_data = schemas.WorkflowData(
                    id=f"template_{template_id}",
                    name=agent_data.name,
                    version="1.0.0",
                    active=True,
                    nodes=[],
                    connections={},
                    start_node_id="start",
                )

        # Create agent data from template
        agent_create_data = schemas.AgentCreate(
            name=agent_data.name,
            description=agent_data.description or template.description,
            category=agent_data.category or template.category,
            workflow=workflow_data,
            is_active=True,
            tags=template.tags or [],
            from_template=True,
            template_id=template_id,
        )

        # Apply customizations if provided
        if agent_data.customizations:
            # Apply any customizations to the workflow or other properties
            if "description" in agent_data.customizations:
                agent_create_data.description = agent_data.customizations["description"]
            if "category" in agent_data.customizations:
                agent_create_data.category = agent_data.customizations["category"]

        return await create_agent(db, agent_create_data, user_id)

    except Exception as e:
        raise ValueError(f"Failed to create agent from template: {str(e)}")

        # ============================================================================
        # HELPER FUNCTIONS
        # ============================================================================

        return await create_agent(db, agent_create_data, user_id)

    except Exception as e:
        raise ValueError(f"Failed to create agent from template: {str(e)}")


async def _get_agent_statistics(
    db: AsyncSession, agent_id: int, user_id: int
) -> Dict[str, Any]:
    """Get agent statistics from call history."""
    # Mock statistics for now
    return {"total_conversations": 0, "success_rate": 0.0, "avg_response_time": 0.0}


def _find_reachable_nodes(node_id: str, connections: Dict[str, Any], reachable: set):
    """Recursively find all reachable nodes from a starting node."""
    if node_id in reachable:
        return

    reachable.add(node_id)

    node_connections = connections.get(node_id)
    if not node_connections:
        return

    # Follow main connections
    if node_connections.main:
        for connection in node_connections.main:
            _find_reachable_nodes(connection.node, connections, reachable)

    # Follow conditional connections
    if node_connections.conditional:
        for path_connections in node_connections.conditional.values():
            for connection in path_connections:
                _find_reachable_nodes(connection.node, connections, reachable)



