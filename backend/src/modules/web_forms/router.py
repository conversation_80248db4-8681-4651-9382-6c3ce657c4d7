"""
Web Forms API Router for embeddable form management.
"""

import logging
import time
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.service import get_current_active_user
from modules.companies import crud as company_crud
from modules.users.models import User as DBUser

from . import crud, models, schemas

logger = logging.getLogger(__name__)

router = APIRouter(tags=["web-forms"])


@router.get("/forms", response_model=schemas.WebFormListResponse)
async def get_web_forms(
    company_id: int = Query(..., description="Company ID"),
    status: Optional[schemas.WebFormSubmissionStatus] = None,
    search: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get web forms for a company."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Get forms and total count
    forms = await crud.get_forms(db, company_id, skip, limit, status, search)
    total = await crud.get_form_count(db, company_id, status)

    return schemas.WebFormListResponse(
        web_forms=forms,
        total=total,
        page=skip // limit + 1,
        per_page=limit,
        has_next=skip + limit < total,
        has_prev=skip > 0,
    )


@router.post("/forms")
async def create_web_form(
    form_data: schemas.FormBuilderConfig,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new web form from form builder."""
    # For now, use a default company ID - in production this would come from the request
    company_id = 1

    # Verify company belongs to user (skip for now in development)
    # company = await company_crud.get_company(db, company_id, current_user.id)
    # if not company:
    #     raise HTTPException(status_code=404, detail="Company not found")

    try:
        # Create the web form
        db_form = models.WebForm(
            company_id=company_id,
            title=form_data.name,
            description=form_data.description,
            web_form_id=f"form_{int(time.time())}",
            status=models.WebFormStatus.DRAFT.value,
            form_type="custom",
            theme=form_data.theme or {},
            is_multi_step=len(form_data.pages) > 1,
            show_progress_bar=form_data.show_progress_bar,
            thank_you_message=form_data.success_message,
            send_confirmation_email=form_data.send_confirmation_email,
            redirect_url=form_data.redirect_url,
            user_id=current_user.id,
        )

        db.add(db_form)
        await db.flush()  # Get the form ID

        # Create form fields from all pages
        field_order = 0
        for page_index, page in enumerate(form_data.pages):
            for field_index, field in enumerate(page.fields):
                db_field = models.WebFormField(
                    form_id=db_form.id,
                    field_id=field.id,
                    title=field.label,
                    description=field.help_text,
                    field_type=field.type,
                    is_required=field.required,
                    order_index=field_order,
                    placeholder_text=field.placeholder,
                    help_text=field.help_text,
                    config={
                        "width": field.width,
                        "options": field.options,
                        "min_value": field.min_value,
                        "max_value": field.max_value,
                        "pattern": field.pattern,
                        "service_categories": field.service_categories,
                        "service_types": field.service_types,
                        "validation": field.validation,
                        "styling": field.styling,
                        "conditional_logic": field.conditional_logic,
                        "field_config": field.config,
                        "page_id": page.id,
                        "page_title": page.title,
                        "page_index": page_index,
                        "field_index": field_index,
                    }
                )
                db.add(db_field)
                field_order += 1

        await db.commit()
        await db.refresh(db_form)

        return {
            "id": db_form.id,
            "web_form_id": db_form.web_form_id,
            "embed_code": f"<script src='https://example.com/embed/{db_form.web_form_id}.js'></script>",
            "form_url": f"https://example.com/forms/{db_form.web_form_id}",
            "message": "Web form created successfully",
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create form: {str(e)}")


@router.post("/")
async def create_web_form_legacy(
    company_id: int,
    form_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new web form (legacy endpoint)."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement web form creation
    return {
        "form_id": "form_123",
        "embed_code": "<script src='https://example.com/embed/form_123.js'></script>",
        "message": "Web form created successfully",
    }


@router.get("/templates")
async def get_form_templates(
    category: Optional[str] = None,
    is_public: bool = True,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get available form templates."""
    # Mock templates for now
    templates = [
        {
            "id": 1,
            "name": "Service Request Form",
            "description": "Complete service booking form with appointment scheduling",
            "category": "service_request",
            "preview_image": "/templates/service-request.png",
            "is_public": True,
            "tags": ["booking", "appointment", "service"],
            "usage_count": 245,
            "rating": 4.8,
            "template_data": {
                "name": "Service Request Form",
                "description": "Professional service booking form",
                "pages": [
                    {
                        "id": "page_1",
                        "title": "Where are you?",
                        "description": "Let us know your location",
                        "fields": [
                            {
                                "id": "zip_code",
                                "type": "zip_code",
                                "label": "ZIP Code",
                                "placeholder": "Enter your ZIP code",
                                "required": True,
                                "width": "half"
                            }
                        ]
                    },
                    {
                        "id": "page_2",
                        "title": "Where should we reach you?",
                        "fields": [
                            {
                                "id": "customer_name",
                                "type": "short_text",
                                "label": "Full Name",
                                "placeholder": "Enter your full name",
                                "required": True
                            },
                            {
                                "id": "phone",
                                "type": "phone",
                                "label": "Phone Number",
                                "required": True
                            },
                            {
                                "id": "email",
                                "type": "email",
                                "label": "Email Address",
                                "required": True
                            }
                        ]
                    }
                ],
                "enable_booking": True,
                "show_pricing": True,
                "dispatch_fee": 75.0
            }
        },
        {
            "id": 2,
            "name": "Quick Quote Request",
            "description": "Simple form for getting service quotes",
            "category": "quote",
            "preview_image": "/templates/quote-request.png",
            "is_public": True,
            "tags": ["quote", "estimate"],
            "usage_count": 156,
            "rating": 4.5,
            "template_data": {
                "name": "Quick Quote Request",
                "pages": [
                    {
                        "id": "page_1",
                        "title": "Tell us about your project",
                        "fields": [
                            {
                                "id": "service_category",
                                "type": "service_category",
                                "label": "What service do you need?",
                                "required": True
                            },
                            {
                                "id": "description",
                                "type": "long_text",
                                "label": "Project Description",
                                "placeholder": "Describe your project in detail...",
                                "required": True
                            }
                        ]
                    }
                ]
            }
        }
    ]

    if category:
        templates = [t for t in templates if t["category"] == category]

    return {"templates": templates}


@router.post("/from-template/{template_id}")
async def create_form_from_template(
    template_id: int,
    company_id: int,
    customizations: Optional[dict] = None,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Create a new form from a template."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement template-based form creation
    return {
        "form_id": f"form_from_template_{template_id}",
        "message": "Form created from template successfully",
        "template_id": template_id
    }


@router.get("/{form_id}/appointments/slots")
async def get_available_appointment_slots(
    form_id: str,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
    service_type: Optional[str] = None,
    zip_code: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
):
    """Get available appointment slots for a form."""
    # Mock appointment slots
    from datetime import datetime, timedelta

    base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    slots = []

    for day in range(7):  # Next 7 days
        current_date = base_date + timedelta(days=day)
        if current_date.weekday() < 5:  # Monday to Friday
            for hour in [9, 11, 13, 15, 17]:  # 9 AM to 5 PM
                slot_time = current_date.replace(hour=hour)
                slots.append({
                    "id": f"slot_{day}_{hour}",
                    "date": slot_time.date().isoformat(),
                    "start_time": slot_time.time().isoformat(),
                    "end_time": (slot_time + timedelta(hours=2)).time().isoformat(),
                    "duration_minutes": 120,
                    "is_available": True,
                    "base_price": 150.0,
                    "dispatch_fee": 75.0,
                    "service_types": ["plumbing", "electrical", "hvac"]
                })

    return {"slots": slots}


@router.post("/{form_id}/submit")
async def submit_form_with_booking(
    form_id: str,
    submission_data: dict,
    db: AsyncSession = Depends(get_db),
):
    """Submit a form and create booking if applicable."""
    # TODO: Implement form submission with booking creation

    booking_reference = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"

    return {
        "submission_id": "sub_123",
        "booking_reference": booking_reference,
        "message": "Form submitted and booking created successfully",
        "next_steps": [
            "You will receive a confirmation email shortly",
            "Our team will contact you to confirm the appointment",
            "Please have your payment method ready for the dispatch fee"
        ]
    }


@router.get("/{form_id}")
async def get_web_form(
    form_id: str,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get a specific web form."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get web form
    raise HTTPException(status_code=404, detail="Web form not found")


@router.get("/{form_id}/submissions")
async def get_web_form_submissions(
    form_id: str,
    company_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get submissions for a web form."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Implement get form submissions
    return []


# Public endpoint for form submissions (no auth required)
@router.post("/{form_id}/submit")
async def submit_web_form(
    form_id: str, submission_data: dict, db: AsyncSession = Depends(get_db)
):
    """Submit a web form (public endpoint)."""
    # TODO: Implement form submission
    return {"submission_id": "submission_123", "message": "Form submitted successfully"}


@router.get("/{form_id}/embed")
async def get_web_form_embed_code(
    form_id: str,
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: DBUser = Depends(get_current_active_user),
):
    """Get embed code for a web form."""
    # Verify company belongs to user
    company = await company_crud.get_company(db, company_id, current_user.id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # TODO: Generate embed code
    return {
        "embed_code": f"<script src='https://example.com/embed/{form_id}.js'></script>",
        "iframe_code": f"<iframe src='https://example.com/forms/{form_id}' width='100%' height='600'></iframe>",
        "direct_link": f"https://example.com/forms/{form_id}",
    }
