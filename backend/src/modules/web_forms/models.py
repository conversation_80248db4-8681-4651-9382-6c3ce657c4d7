"""
Web Form Plugin Models for Typeform-like guided forms.

Supports embeddable forms with prefill capabilities and SMS integration.
"""

from enum import Enum as PyEnum

from sqlalchemy import (
    <PERSON>SO<PERSON>,
    Boolean,
    Column,
    DateTime,
    Float,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class WebFormStatus(PyEnum):
    """Form status options."""

    DRAFT = "draft"
    PUBLISHED = "published"
    PAUSED = "paused"
    ARCHIVED = "archived"


class WebFieldType(PyEnum):
    """Form field types."""

    # Basic Fields
    SHORT_TEXT = "short_text"
    LONG_TEXT = "long_text"
    EMAIL = "email"
    PHONE = "phone"
    NUMBER = "number"
    DROPDOWN = "dropdown"
    MULTIPLE_CHOICE = "multiple_choice"
    CHECKBOXES = "checkboxes"
    DATE = "date"
    TIME = "time"
    FILE_UPLOAD = "file_upload"
    RATING = "rating"
    YES_NO = "yes_no"
    WEBSITE = "website"
    LEGAL = "legal"
    PAYMENT = "payment"

    # Home Service Specific Fields
    ADDRESS = "address"
    ZIP_CODE = "zip_code"
    SERVICE_CATEGORY = "service_category"
    SERVICE_TYPE = "service_type"
    SERVICE_OPTIONS = "service_options"
    APPOINTMENT_SLOTS = "appointment_slots"
    CALENDAR_VIEW = "calendar_view"
    PRICING_DISPLAY = "pricing_display"

    # Advanced Fields
    SECTION_BREAK = "section_break"
    HTML_CONTENT = "html_content"
    CONDITIONAL_LOGIC = "conditional_logic"


class WebFormSubmissionStatus(PyEnum):
    """Form submission status."""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    SPAM = "spam"


class WebForm(Base):
    """
    Main web form model for creating embeddable forms.
    """

    __tablename__ = "web_forms"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Form Information
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    web_form_id = Column(
        String(100), nullable=False, unique=True, index=True
    )  # Public form ID

    # Form Configuration
    status = Column(
        String(50), nullable=False, default=WebFormStatus.DRAFT.value, index=True
    )
    form_type = Column(
        String(50), default="standard"
    )  # standard, booking, contact, survey

    # Design & Branding
    theme = Column(JSON, nullable=True, default=dict)  # Colors, fonts, styling
    logo_url = Column(String(500), nullable=True)
    background_image_url = Column(String(500), nullable=True)
    custom_css = Column(Text, nullable=True)

    # Form Behavior
    is_multi_step = Column(Boolean, default=True)
    show_progress_bar = Column(Boolean, default=True)
    allow_multiple_submissions = Column(Boolean, default=True)
    require_login = Column(Boolean, default=False)

    # Submission Settings
    redirect_url = Column(String(500), nullable=True)
    thank_you_message = Column(Text, nullable=True)
    send_confirmation_email = Column(Boolean, default=False)
    confirmation_email_template = Column(Text, nullable=True)

    # Integration Settings
    webhook_url = Column(String(500), nullable=True)
    webhook_events = Column(JSON, nullable=True, default=list)

    # SMS Integration
    sms_enabled = Column(Boolean, default=False)
    sms_notification_number = Column(String(20), nullable=True)
    sms_template = Column(Text, nullable=True)

    # Agent Integration
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    auto_create_conversation = Column(Boolean, default=False)

    # Analytics & Tracking
    google_analytics_id = Column(String(50), nullable=True)
    facebook_pixel_id = Column(String(50), nullable=True)

    # SEO & Meta
    meta_title = Column(String(255), nullable=True)
    meta_description = Column(Text, nullable=True)
    meta_keywords = Column(JSON, nullable=True, default=list)

    # Access Control
    password_protected = Column(Boolean, default=False)
    access_password = Column(String(255), nullable=True)
    allowed_domains = Column(JSON, nullable=True, default=list)

    # Statistics
    total_views = Column(Integer, default=0)
    total_submissions = Column(Integer, default=0)
    completion_rate = Column(Float, default=0.0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    published_at = Column(DateTime(timezone=True), nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationships
    company = relationship("Company", back_populates="web_forms")
    agent = relationship("Agent")
    user = relationship("User", foreign_keys=[user_id])
    fields = relationship(
        "WebFormField",
        back_populates="form",
        cascade="all, delete-orphan",
        order_by="WebFormField.order_index",
    )
    submissions = relationship(
        "WebFormSubmission", back_populates="web_form", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return (
            f"<WebForm(id={self.id}, form_id='{self.form_id}', title='{self.title}')>"
        )


class WebFormField(Base):
    """
    Individual fields within a web form.
    """

    __tablename__ = "web_form_fields"

    id = Column(Integer, primary_key=True, index=True)
    form_id = Column(Integer, ForeignKey("web_forms.id"), nullable=False)

    # Field Information
    field_id = Column(String(100), nullable=False, index=True)  # Unique within form
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    # Field Configuration
    field_type = Column(
        String(50), nullable=False, default=WebFieldType.SHORT_TEXT.value
    )
    is_required = Column(Boolean, default=False)
    order_index = Column(Integer, nullable=False, default=0)

    # Field Options
    options = Column(
        JSON, nullable=True, default=list
    )  # For dropdown, multiple choice, etc.
    validation_rules = Column(JSON, nullable=True, default=dict)
    placeholder_text = Column(String(255), nullable=True)
    help_text = Column(Text, nullable=True)

    # Conditional Logic
    show_conditions = Column(
        JSON, nullable=True, default=list
    )  # When to show this field
    hide_conditions = Column(
        JSON, nullable=True, default=list
    )  # When to hide this field

    # Prefill Configuration
    prefill_enabled = Column(Boolean, default=False)
    prefill_source = Column(
        String(100), nullable=True
    )  # url_param, cookie, session, etc.
    prefill_key = Column(String(100), nullable=True)

    # Field Styling
    custom_css_class = Column(String(255), nullable=True)
    field_width = Column(String(20), default="full")  # full, half, third, quarter

    # File Upload Settings (for file fields)
    max_file_size = Column(Integer, nullable=True)  # in bytes
    allowed_file_types = Column(JSON, nullable=True, default=list)
    max_files = Column(Integer, default=1)

    # Rating Settings (for rating fields)
    rating_scale = Column(Integer, default=5)  # 1-5, 1-10, etc.
    rating_labels = Column(JSON, nullable=True, default=list)

    # Status
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    form = relationship("WebForm", back_populates="fields")
    responses = relationship(
        "WebFormResponse", back_populates="field", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<WebFormField(id={self.id}, field_id='{self.field_id}', type='{self.field_type}')>"


class WebFormSubmission(Base):
    """
    Form submissions from users.
    """

    __tablename__ = "web_form_submissions"

    id = Column(Integer, primary_key=True, index=True)
    web_form_id = Column(Integer, ForeignKey("web_forms.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Submission Information
    submission_id = Column(String(100), nullable=False, unique=True, index=True)

    # User Information
    user_ip = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    referrer_url = Column(String(500), nullable=True)

    # Contact Information (extracted from responses)
    contact_name = Column(String(255), nullable=True)
    contact_email = Column(String(255), nullable=True, index=True)
    contact_phone = Column(String(20), nullable=True, index=True)

    # Submission Status
    status = Column(
        String(50),
        nullable=False,
        default=WebFormSubmissionStatus.PENDING.value,
        index=True,
    )
    completion_percentage = Column(Float, default=0.0)

    # Timing
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    time_to_complete = Column(Integer, nullable=True)  # seconds

    # Quality & Validation
    is_spam = Column(Boolean, default=False)
    spam_score = Column(Float, nullable=True)
    validation_errors = Column(JSON, nullable=True, default=list)

    # Integration Results
    webhook_sent = Column(Boolean, default=False)
    webhook_response = Column(JSON, nullable=True, default=dict)
    sms_sent = Column(Boolean, default=False)
    email_sent = Column(Boolean, default=False)

    # Business Integration
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=True)
    booking_request_id = Column(
        Integer, ForeignKey("booking_requests.id"), nullable=True
    )

    # UTM & Tracking
    utm_source = Column(String(255), nullable=True)
    utm_medium = Column(String(255), nullable=True)
    utm_campaign = Column(String(255), nullable=True)
    utm_term = Column(String(255), nullable=True)
    utm_content = Column(String(255), nullable=True)

    # Metadata
    extra_metadata = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    web_form = relationship("WebForm", back_populates="submissions")
    company = relationship("Company")
    conversation = relationship("Conversation")
    booking_request = relationship("BookingRequest")
    responses = relationship(
        "WebFormResponse", back_populates="submission", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<WebFormSubmission(id={self.id}, submission_id='{self.submission_id}', status='{self.status}')>"


class WebFormResponse(Base):
    """
    Individual field responses within a form submission.
    """

    __tablename__ = "web_form_responses"

    id = Column(Integer, primary_key=True, index=True)
    submission_id = Column(
        Integer, ForeignKey("web_form_submissions.id"), nullable=False
    )
    field_id = Column(Integer, ForeignKey("web_form_fields.id"), nullable=False)

    # Response Data
    response_value = Column(Text, nullable=True)  # The actual response
    response_data = Column(
        JSON, nullable=True, default=dict
    )  # Structured data for complex fields

    # File Upload Data (for file fields)
    file_url = Column(String(500), nullable=True)
    file_name = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)
    file_type = Column(String(100), nullable=True)

    # Response Metadata
    response_time = Column(
        Integer, nullable=True
    )  # Time spent on this field in seconds
    is_prefilled = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    submission = relationship("WebFormSubmission", back_populates="responses")
    field = relationship("WebFormField", back_populates="responses")

    def __repr__(self):
        return f"<WebFormResponse(id={self.id}, field_id={self.field_id}, value='{self.response_value[:50]}...')>"


class WebFormAnalytics(Base):
    """
    Analytics for form performance and user behavior.
    """

    __tablename__ = "web_form_analytics"

    id = Column(Integer, primary_key=True, index=True)
    web_form_id = Column(Integer, ForeignKey("web_forms.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False, default="daily")

    # Traffic Metrics
    total_views = Column(Integer, default=0)
    unique_visitors = Column(Integer, default=0)
    returning_visitors = Column(Integer, default=0)

    # Conversion Metrics
    total_submissions = Column(Integer, default=0)
    completed_submissions = Column(Integer, default=0)
    abandoned_submissions = Column(Integer, default=0)
    conversion_rate = Column(Float, default=0.0)  # percentage

    # Performance Metrics
    average_completion_time = Column(Integer, default=0)  # seconds
    bounce_rate = Column(Float, default=0.0)  # percentage
    drop_off_points = Column(JSON, nullable=True, default=list)

    # Traffic Sources
    traffic_sources = Column(JSON, nullable=True, default=dict)
    referrer_domains = Column(JSON, nullable=True, default=dict)

    # Device & Browser
    device_breakdown = Column(JSON, nullable=True, default=dict)
    browser_breakdown = Column(JSON, nullable=True, default=dict)

    # Geographic Data
    country_breakdown = Column(JSON, nullable=True, default=dict)
    city_breakdown = Column(JSON, nullable=True, default=dict)

    # Field Performance
    field_completion_rates = Column(JSON, nullable=True, default=dict)
    field_average_times = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    web_form = relationship("WebForm")


class FormTemplate(Base):
    """
    Pre-built form templates for common home service scenarios.
    """

    __tablename__ = "form_templates"

    id = Column(Integer, primary_key=True, index=True)

    # Template Information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=False)  # service_request, quote, contact, etc.

    # Template Configuration
    template_data = Column(JSON, nullable=False)  # Complete form structure
    preview_image = Column(String(500), nullable=True)
    is_public = Column(Boolean, default=True)
    tags = Column(JSON, nullable=True, default=list)

    # Usage Statistics
    usage_count = Column(Integer, default=0)
    rating = Column(Float, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Optional company association for private templates
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=True)


class AppointmentSlot(Base):
    """
    Available appointment slots for service bookings.
    """

    __tablename__ = "appointment_slots"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Slot Information
    date = Column(DateTime(timezone=True), nullable=False)
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=False)
    duration_minutes = Column(Integer, nullable=False, default=60)

    # Availability
    is_available = Column(Boolean, default=True)
    max_bookings = Column(Integer, default=1)
    current_bookings = Column(Integer, default=0)

    # Service Information
    service_types = Column(JSON, nullable=True, default=list)  # Which services this slot supports
    technician_id = Column(Integer, nullable=True)  # Optional technician assignment

    # Pricing
    base_price = Column(Float, nullable=True)
    dispatch_fee = Column(Float, nullable=True)

    # Location
    service_areas = Column(JSON, nullable=True, default=list)  # ZIP codes or areas served

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class FormBooking(Base):
    """
    Bookings created through form submissions.
    """

    __tablename__ = "form_bookings"

    id = Column(Integer, primary_key=True, index=True)
    submission_id = Column(Integer, ForeignKey("web_form_submissions.id"), nullable=False)
    appointment_slot_id = Column(Integer, ForeignKey("appointment_slots.id"), nullable=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Booking Information
    booking_reference = Column(String(50), unique=True, nullable=False)
    status = Column(String(50), default="pending")  # pending, confirmed, completed, cancelled

    # Customer Information
    customer_name = Column(String(255), nullable=False)
    customer_email = Column(String(255), nullable=True)
    customer_phone = Column(String(50), nullable=True)

    # Service Information
    service_category = Column(String(100), nullable=True)
    service_type = Column(String(100), nullable=True)
    service_description = Column(Text, nullable=True)

    # Location Information
    service_address = Column(Text, nullable=True)
    service_city = Column(String(100), nullable=True)
    service_state = Column(String(50), nullable=True)
    service_zip_code = Column(String(20), nullable=True)
    location_notes = Column(Text, nullable=True)

    # Appointment Information
    scheduled_date = Column(DateTime(timezone=True), nullable=True)
    scheduled_start_time = Column(DateTime(timezone=True), nullable=True)
    scheduled_end_time = Column(DateTime(timezone=True), nullable=True)

    # Pricing
    estimated_price = Column(Float, nullable=True)
    dispatch_fee = Column(Float, nullable=True)
    total_amount = Column(Float, nullable=True)

    # Additional Information
    special_instructions = Column(Text, nullable=True)
    attachments = Column(JSON, nullable=True, default=list)

    # Terms and Conditions
    terms_accepted = Column(Boolean, default=False)
    terms_accepted_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    submission = relationship("WebFormSubmission", back_populates="booking")
    appointment_slot = relationship("AppointmentSlot")


    # Add relationship to WebFormSubmission
    WebFormSubmission.booking = relationship("FormBooking", back_populates="submission", uselist=False)

    def __repr__(self):
        return f"<WebFormAnalytics(form_id={self.form_id}, date='{self.date}')>"
