import asyncio
import base64
import json
import logging
from typing import AsyncGenerator

import g711  # type: ignore
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.responses import Response
from starlette.websockets import WebSocketState
from twilio.rest import Client
from twilio.twiml.voice_response import Connect, VoiceResponse

from core.config import get_settings
from core.db.database import AsyncSessionLocal
from core.services.conversation_manager import ConversationManager
from core.websocket.connection_manager import connection_manager
from modules.customers import crud as customer_crud

# STT and TTS services will be handled by Dify

from . import crud, schemas

logger = logging.getLogger(__name__)
settings = get_settings()

#
# Twilio Service Singleton
#


class TwilioService:
    def __init__(self):
        self.client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
        self.from_number = settings.TWILIO_PHONE_NUMBER

    def make_outbound_call(self, to_number: str, call_history_id: int):
        try:
            twiml_url = f"https://{settings.BASE_URL}/api/call/twiml/{call_history_id}"
            call = self.client.calls.create(
                to=to_number, from_=self.from_number, url=twiml_url, method="POST"
            )
            logger.info(f"Outbound call initiated: {call.sid} to {to_number}")
            return {"success": True, "call_sid": call.sid}
        except Exception as e:
            logger.error(f"Failed to initiate call to {to_number}: {str(e)}")
            return {"success": False, "error": str(e)}


twilio_service = TwilioService()


#
# Private Helper Functions
#


async def _create_call_history_entry(
    db: AsyncSession, platform: str, request: schemas.CallRequest, user_id: int
):
    customer = await customer_crud.get_customer_by_domain(
        db, domain="test.customer", user_id=user_id
    )
    if not customer:
        raise HTTPException(status_code=500, detail="Default test customer not found.")

    call_sid = f"{platform}_call_{asyncio.get_running_loop().time()}"
    if platform == "phone":
        call_sid = "temp_sid"  # Placeholder, will be updated by Twilio webhook

    return await crud.create_call_history(
        db,
        schemas.CallHistoryCreate(
            customer_id=customer.id,
            call_sid=call_sid,
            call_status="initiated",
            system_prompt=request.custom_prompt,
            first_message=request.custom_first_message,
            call_metadata={"platform": platform, "webhook_data": request.webhook_data},
        ),
        user_id=user_id,
    )


#
# Service Functions
#


async def initiate_call(
    call_request: schemas.CallRequest, db: AsyncSession, user_id: int
):
    if call_request.type == "voice":
        return await initiate_phone_call(call_request, db, user_id)
    elif call_request.type == "web":
        return await initiate_web_call(call_request, db, user_id)
    elif call_request.type == "text":
        return await initiate_text_chat(call_request, db, user_id)
    else:
        raise HTTPException(status_code=400, detail="Invalid call type")


async def initiate_phone_call(
    call_request: schemas.CallRequest, db: AsyncSession, user_id: int
):
    """
    Initiates a phone call using Twilio.
    """
    logger.info(f"Initiating phone call to {call_request.phone_number}")

    if not call_request.phone_number:
        raise HTTPException(
            status_code=400, detail="Phone number is required for voice calls"
        )

    call_history = await _create_call_history_entry(db, "phone", call_request, user_id)

    # Make the outbound call using Twilio
    result = twilio_service.make_outbound_call(
        call_request.phone_number, call_history.id
    )

    if result["success"]:
        # Update call history with actual call SID from Twilio
        await crud.update_call_history(db, call_history.id, call_sid=result["call_sid"])
        return {
            "success": True,
            "call_history_id": call_history.id,
            "call_sid": result["call_sid"],
            "message": "Phone call initiated successfully.",
        }
    else:
        await crud.update_call_history(db, call_history.id, call_status="failed")
        raise HTTPException(
            status_code=500, detail=f"Failed to initiate call: {result['error']}"
        )


async def initiate_web_call(
    call_request: schemas.CallRequest, db: AsyncSession, user_id: int
):
    """
    Initiates a web-based voice call session.
    """
    logger.info("Initiating web call session.")
    call_history = await _create_call_history_entry(
        db, "web_call", call_request, user_id
    )
    return {
        "success": True,
        "call_history_id": call_history.id,
        "message": "Web call initiated successfully.",
    }


async def initiate_text_chat(
    call_request: schemas.CallRequest, db: AsyncSession, user_id: int
):
    """
    Initiates a text-based chat session.
    """
    logger.info("Initiating text chat session.")
    call_history = await _create_call_history_entry(
        db, "text_chat", call_request, user_id
    )
    return {
        "success": True,
        "call_history_id": call_history.id,
        "message": "Text chat initiated successfully.",
    }


#
# WebSocket Handlers
#


async def handle_web_call_conversation(websocket: WebSocket):
    """
    Handles web-based voice call conversation with STT/TTS integration.
    """
    await websocket.accept()
    logger.info("WebSocket connection accepted for web call.")
    conversation_manager = ConversationManager(db_session_factory=AsyncSessionLocal)

    try:
        init_message = await websocket.receive_json()
        data = init_message.get("data", init_message)
        call_history_id = data.get("call_history_id") or data.get("callHistoryId")
        if not call_history_id:
            raise ValueError("call_history_id not found in init message")

        # STT and TTS services are now handled by Dify
        # stt_service = get_stt_service()
        # tts_service = get_tts_service()

        async def input_handler(ws: WebSocket, speech_config: dict) -> str | None:
            try:
                message = await ws.receive_json()
                if message.get("type") == "audio_data":
                    # Handle audio input for STT
                    audio_data = base64.b64decode(message.get("audio", ""))
                    if audio_data:
                        transcript = await stt_service.transcribe_audio(audio_data)
                        if transcript and transcript.strip():
                            logger.info(f"STT transcript: {transcript}")
                            return transcript
                elif message.get("type") == "user_message":
                    # Handle text input as fallback
                    return message.get("text")
                return None
            except WebSocketDisconnect:
                logger.info("Client disconnected from web call.")
                return None

        async def output_handler(
            text_chunk_generator: AsyncGenerator[str, None],
        ) -> str:
            full_text = ""
            async for chunk in text_chunk_generator:
                full_text += chunk
                # Send intermediate text chunks
                await websocket.send_json(
                    {
                        "type": "transcript",
                        "sender": "agent",
                        "transcript": full_text,
                        "is_final": False,
                    }
                )

            # Generate and send audio for the final text
            try:
                audio_data = await tts_service.synthesize_speech(full_text)
                if audio_data:
                    audio_base64 = base64.b64encode(audio_data).decode("utf-8")
                    await websocket.send_json(
                        {
                            "type": "audio_response",
                            "audio": audio_base64,
                            "transcript": full_text,
                            "is_final": True,
                        }
                    )
                else:
                    # Fallback to text-only response
                    await websocket.send_json(
                        {
                            "type": "transcript",
                            "sender": "agent",
                            "transcript": full_text,
                            "is_final": True,
                        }
                    )
            except Exception as e:
                logger.error(f"Error generating TTS audio: {e}")
                # Fallback to text-only response
                await websocket.send_json(
                    {
                        "type": "transcript",
                        "sender": "agent",
                        "transcript": full_text,
                        "is_final": True,
                    }
                )

            return full_text

        async def first_message_handler(text: str):
            # Generate and send audio for first message
            try:
                audio_data = await tts_service.synthesize_speech(text)
                if audio_data:
                    audio_base64 = base64.b64encode(audio_data).decode("utf-8")
                    await websocket.send_json(
                        {
                            "type": "audio_response",
                            "audio": audio_base64,
                            "transcript": text,
                            "is_final": True,
                        }
                    )
                else:
                    await websocket.send_json(
                        {
                            "type": "transcript",
                            "sender": "agent",
                            "transcript": text,
                            "is_final": True,
                        }
                    )
            except Exception as e:
                logger.error(f"Error generating first message TTS: {e}")
                await websocket.send_json(
                    {
                        "type": "transcript",
                        "sender": "agent",
                        "transcript": text,
                        "is_final": True,
                    }
                )

        async def get_init_message():
            async with AsyncSessionLocal() as db:
                call_history = await crud.get_call_history(db, call_history_id)
                return {
                    "call_history_id": call_history_id,
                    "system_prompt": call_history.system_prompt
                    if call_history
                    else None,
                    "first_message": call_history.first_message
                    if call_history
                    else None,
                    "webhook_data": call_history.call_metadata.get("webhook_data")
                    if call_history and call_history.call_metadata
                    else None,
                }

        await conversation_manager.handle_conversation(
            call_history_id=call_history_id,
            input_handler=input_handler,
            output_handler=output_handler,
            first_message_handler=first_message_handler,
            get_init_message=get_init_message,
            websocket=websocket,
            agent_config_data=data.get("agent_config"),
        )
        await websocket.close()

    except Exception as e:
        logger.error(f"Error in web call endpoint: {e}", exc_info=True)
    finally:
        logger.info("Closing WebSocket connection for web call.")


async def handle_text_chat_conversation(websocket: WebSocket):
    await websocket.accept()
    logger.info("WebSocket connection accepted for text chat.")
    conversation_manager = ConversationManager(db_session_factory=AsyncSessionLocal)

    try:
        init_message = await websocket.receive_json()
        data = init_message.get("data", init_message)
        call_history_id = data.get("call_history_id") or data.get("callHistoryId")
        if not call_history_id:
            raise ValueError("call_history_id not found in init message")

        async def input_handler(ws: WebSocket, speech_config: dict) -> str | None:
            try:
                user_message_json = await ws.receive_json()
                return (
                    user_message_json.get("text")
                    if user_message_json.get("type") == "user_message"
                    else None
                )
            except WebSocketDisconnect:
                logger.info("Client disconnected from text chat.")
                return None

        async def output_handler(
            text_chunk_generator: AsyncGenerator[str, None],
        ) -> str:
            full_text = ""
            # First, send an empty non-final message to establish the entry
            await websocket.send_json(
                {
                    "type": "transcript",
                    "sender": "agent",
                    "transcript": "",
                    "is_final": False,
                }
            )
            async for chunk in text_chunk_generator:
                full_text += chunk
                # Send intermediate chunks as non-final, allowing frontend to append
                await websocket.send_json(
                    {
                        "type": "transcript_chunk",
                        "sender": "agent",
                        "transcript": chunk,
                    }
                )
            # The final state of the text is sent once, marked as final
            await websocket.send_json(
                {
                    "type": "transcript",
                    "sender": "agent",
                    "transcript": full_text,
                    "is_final": True,
                }
            )
            return full_text

        async def first_message_handler(text: str):
            await websocket.send_json(
                {
                    "type": "transcript",
                    "sender": "agent",
                    "transcript": text,
                    "is_final": True,
                }
            )

        async def get_init_message():
            async with AsyncSessionLocal() as db:
                call_history = await crud.get_call_history(db, call_history_id)
                return {
                    "call_history_id": call_history_id,
                    "system_prompt": call_history.system_prompt
                    if call_history
                    else None,
                    "first_message": call_history.first_message
                    if call_history
                    else None,
                    "webhook_data": call_history.call_metadata.get("webhook_data")
                    if call_history and call_history.call_metadata
                    else None,
                }

        await conversation_manager.handle_conversation(
            call_history_id=call_history_id,
            input_handler=input_handler,
            output_handler=output_handler,
            first_message_handler=first_message_handler,
            get_init_message=get_init_message,
            websocket=websocket,
            agent_config_data=data.get("agent_config"),
        )

    except Exception as e:
        logger.error(f"Error in text chat endpoint: {e}", exc_info=True)
    finally:
        logger.info("Closing WebSocket connection for text chat.")


async def handle_phone_relay_conversation(websocket: WebSocket, call_history_id: int):
    # This function needs access to a db session.
    # Since it's called directly from the router, we'll create one.
    async with AsyncSessionLocal() as db:
        await _handle_phone_relay_conversation(websocket, call_history_id, db)


async def _handle_phone_relay_conversation(
    websocket: WebSocket, call_history_id: int, db: AsyncSession
):
    await websocket.accept()
    logger.info(f"WebSocket connection accepted for phone relay: {call_history_id}")

    conversation_manager = ConversationManager(db_session_factory=AsyncSessionLocal)

    # STT and TTS services are now handled by Dify
    # stt_service = get_stt_service()
    # tts_service = get_tts_service()

    # Buffer for accumulating audio chunks
    audio_buffer = bytearray()

    try:

        async def input_handler(ws: WebSocket, speech_config: dict) -> str | None:
            nonlocal audio_buffer
            try:
                while True:
                    message = await ws.receive_text()
                    try:
                        data = json.loads(message)
                        event = data.get("event")

                        if event == "media":
                            # Twilio sends audio as base64-encoded μ-law
                            payload = data.get("media", {}).get("payload", "")
                            if payload:
                                # Decode base64 and convert from μ-law to linear PCM
                                mulaw_data = base64.b64decode(payload)
                                pcm_data = g711.decode_ulaw(mulaw_data)
                                audio_buffer.extend(pcm_data)

                                # Process audio when we have enough data (e.g., 1 second worth)
                                if len(audio_buffer) >= 8000:  # 8kHz * 1 second
                                    transcript = await stt_service.transcribe_audio(
                                        bytes(audio_buffer)
                                    )
                                    audio_buffer.clear()

                                    if transcript and transcript.strip():
                                        logger.info(
                                            f"Phone STT transcript: {transcript}"
                                        )
                                        return transcript

                        elif event == "start":
                            logger.info("Twilio media stream started")
                        elif event == "stop":
                            logger.info("Twilio media stream stopped")
                            return None

                    except json.JSONDecodeError:
                        logger.warning(f"Invalid JSON received: {message}")
                        continue

            except WebSocketDisconnect:
                logger.info("Twilio media stream disconnected.")
                return None

        async def output_handler(
            text_chunk_generator: AsyncGenerator[str, None],
        ) -> str:
            full_text = ""
            async for chunk in text_chunk_generator:
                full_text += chunk

            # Generate audio and send back to Twilio
            try:
                audio_data = await tts_service.synthesize_speech(full_text)
                if audio_data:
                    # Convert PCM to μ-law and base64 encode for Twilio
                    mulaw_data = g711.encode_ulaw(audio_data)
                    payload = base64.b64encode(mulaw_data).decode("utf-8")

                    # Send audio back to Twilio
                    media_message = {
                        "event": "media",
                        "streamSid": "stream_sid_placeholder",  # This should be extracted from the start event
                        "media": {"payload": payload},
                    }
                    await websocket.send_text(json.dumps(media_message))
                    logger.info(f"Sent TTS audio to phone: {full_text}")
            except Exception as e:
                logger.error(f"Error generating phone TTS audio: {e}")

            return full_text

        async def first_message_handler(text: str):
            # Generate and send audio for first message
            try:
                audio_data = await tts_service.synthesize_speech(text)
                if audio_data:
                    mulaw_data = g711.encode_ulaw(audio_data)
                    payload = base64.b64encode(mulaw_data).decode("utf-8")

                    media_message = {
                        "event": "media",
                        "streamSid": "stream_sid_placeholder",
                        "media": {"payload": payload},
                    }
                    await websocket.send_text(json.dumps(media_message))
                    logger.info(f"Sent first message TTS to phone: {text}")
            except Exception as e:
                logger.error(f"Error generating first message phone TTS: {e}")

        async def get_init_message():
            call_history = await crud.get_call_history(db, call_history_id)
            return {
                "call_history_id": call_history_id,
                "system_prompt": call_history.system_prompt if call_history else None,
                "first_message": call_history.first_message if call_history else None,
                "webhook_data": call_history.call_metadata.get("webhook_data")
                if call_history and call_history.call_metadata
                else None,
            }

        await conversation_manager.handle_conversation(
            call_history_id=call_history_id,
            input_handler=input_handler,
            output_handler=output_handler,
            first_message_handler=first_message_handler,
            get_init_message=get_init_message,
            websocket=websocket,
            agent_config_data=None,  # Phone relay doesn't have an init message with config
        )

    except Exception as e:
        logger.error(f"Error in phone relay conversation: {e}", exc_info=True)
    finally:
        logger.info(f"Closing WebSocket connection for phone relay: {call_history_id}")


#
# TwiML and Webhook Handlers
#


async def handle_generate_twiml(request: Request, call_history_id: int):
    response = VoiceResponse()
    connect = Connect()
    connect.stream(
        name="RealtimeSTT",
        url=f"wss://{settings.BASE_URL}/api/call/ws/phone-relay/{call_history_id}",
    )
    response.append(connect)
    response.pause(length=60)
    logger.info(
        f"Generated TwiML for call_history_id {call_history_id}: {str(response)}"
    )
    return Response(content=str(response), media_type="application/xml")


async def handle_inbound_call(request: Request, db: AsyncSession):
    """
    Handle inbound Twilio calls and route them to appropriate agents.
    """
    from .routing_service import routing_service

    form_data = await request.form()
    from_number = form_data.get("From")
    to_number = form_data.get("To")
    call_sid = form_data.get("CallSid")

    logger.info(f"Inbound call from {from_number} to {to_number}, CallSid: {call_sid}")

    try:
        # Route the call to appropriate agent
        agent_config = await routing_service.route_inbound_call(to_number, db)

        if not agent_config:
            # No agent found - play error message and hangup
            response = VoiceResponse()
            response.say(
                "Sorry, we are unable to take your call at this time. Please try again later."
            )
            response.hangup()
            return Response(content=str(response), media_type="application/xml")

        # Create call history entry for inbound call
        call_request = schemas.CallRequest(
            type="voice",
            phone_number=from_number,
            customer_id=1,  # Default customer for now
            context={"inbound": True, "to_number": to_number},
        )

        call_history = await _create_call_history_entry(db, "phone", call_request)

        # Update call history with Twilio call SID
        await crud.update_call_history(db, call_history.id, call_sid=call_sid)

        # Generate TwiML to connect to WebSocket
        response = VoiceResponse()
        connect = Connect()
        connect.stream(
            name="RealtimeSTT",
            url=f"wss://{settings.BASE_URL}/api/call/ws/phone-relay/{call_history.id}",
        )
        response.append(connect)
        response.pause(length=60)

        logger.info(f"Routed inbound call to agent, call_history_id: {call_history.id}")
        return Response(content=str(response), media_type="application/xml")

    except Exception as e:
        logger.error(f"Error handling inbound call: {e}", exc_info=True)
        response = VoiceResponse()
        response.say(
            "Sorry, we are experiencing technical difficulties. Please try again later."
        )
        response.hangup()
        return Response(content=str(response), media_type="application/xml")


async def handle_twilio_status_webhook(
    call_history_id: int, request: Request, db: AsyncSession
):
    form_data = await request.form()
    call_sid = form_data.get("CallSid")
    call_status = form_data.get("CallStatus")
    logger.info(
        f"Twilio status webhook for call {call_sid} (history ID {call_history_id}): {call_status}"
    )
    await crud.update_call_history(
        db, call_history_id, call_status=call_status, call_sid=call_sid
    )
    return {"status": "ok"}


async def handle_websocket_endpoint(
    websocket: WebSocket, call_history_id: int, db: AsyncSession
):
    await connection_manager.connect(websocket, call_history_id)
    logger.info(
        f"WebSocket connection accepted for frontend updates: {call_history_id}"
    )
    try:
        while True:
            # Keep the connection alive to receive updates, but we don't expect messages from the client.
            # This will raise WebSocketDisconnect when the client closes the connection.
            await websocket.receive_text()
    except WebSocketDisconnect:
        logger.info(
            f"Frontend updates WebSocket disconnected for call: {call_history_id}"
        )
    except Exception as e:
        logger.error(
            f"Error in frontend updates WebSocket handler for call {call_history_id}: {e}",
            exc_info=True,
        )
    finally:
        connection_manager.disconnect(call_history_id)
        logger.info(
            f"Closing frontend updates WebSocket connection for call: {call_history_id}"
        )
        if websocket.client_state != WebSocketState.DISCONNECTED:
            try:
                await websocket.close()
            except RuntimeError as e:
                logger.warning(
                    f"Ignoring error during websocket close for call {call_history_id}: {e}"
                )
