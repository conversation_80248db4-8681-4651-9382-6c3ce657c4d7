"""
Company Models for Home Service Platform

Represents home service companies that use the platform.
"""

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class Company(Base):
    """
    Company model representing home service businesses.
    Each company can have multiple agents, phone numbers, and service offerings.
    """

    __tablename__ = "companies"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(
        Integer, ForeignKey("users.id"), nullable=False
    )  # Owner/admin user

    # Basic Company Information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Contact Information
    phone = Column(String(20), nullable=True)
    email = Column(String(255), nullable=True)
    website = Column(String(255), nullable=True)

    # Address Information
    address = Column(String(500), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(50), nullable=True)
    zip_code = Column(String(20), nullable=True)
    country = Column(String(100), nullable=True, default="US")

    # Business Information
    business_type = Column(
        String(100), nullable=True
    )  # e.g., "plumbing", "electrical", "hvac"
    license_number = Column(String(100), nullable=True)
    insurance_info = Column(JSON, nullable=True)

    # Operating Information
    business_hours = Column(JSON, nullable=True)  # Operating hours by day
    timezone = Column(String(50), nullable=True, default="UTC")
    service_areas = Column(JSON, nullable=True)  # List of service areas/zip codes

    # Branding & Customization
    logo_url = Column(String(500), nullable=True)
    brand_colors = Column(JSON, nullable=True)  # Primary, secondary colors
    custom_css = Column(Text, nullable=True)  # Custom CSS for widgets

    # Settings & Configuration
    settings = Column(JSON, nullable=True, default=dict)  # General settings
    notification_settings = Column(JSON, nullable=True, default=dict)
    integration_settings = Column(JSON, nullable=True, default=dict)

    # Status & Control
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)  # Verification status
    subscription_tier = Column(String(50), nullable=True, default="basic")

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="companies")
    agents = relationship(
        "Agent", back_populates="company", cascade="all, delete-orphan"
    )
    phone_numbers = relationship(
        "PhoneNumber", back_populates="company", cascade="all, delete-orphan"
    )
    knowledge_documents = relationship(
        "KnowledgeDocument", back_populates="company", cascade="all, delete-orphan"
    )
    service_catalog = relationship(
        "ServiceCatalogItem", back_populates="company", cascade="all, delete-orphan"
    )
    bookings = relationship(
        "BookingRequest", back_populates="company", cascade="all, delete-orphan"
    )
    web_forms = relationship(
        "WebForm", back_populates="company", cascade="all, delete-orphan"
    )
    chat_sessions = relationship(
        "ChatSession", back_populates="company", cascade="all, delete-orphan"
    )
    faqs = relationship(
        "FAQ", back_populates="company", cascade="all, delete-orphan"
    )
    knowledge_search_indices = relationship(
        "KnowledgeSearchIndex", back_populates="company", cascade="all, delete-orphan"
    )
    knowledge_analytics = relationship(
        "KnowledgeAnalytics", back_populates="company", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Company(id={self.id}, name='{self.name}', business_type='{self.business_type}')>"


class CompanyMember(Base):
    """
    Company team members with different roles and permissions.
    """

    __tablename__ = "company_members"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Role & Permissions
    role = Column(
        String(50), nullable=False, default="member"
    )  # owner, admin, manager, member
    permissions = Column(JSON, nullable=True, default=list)  # Specific permissions

    # Member Information
    title = Column(String(100), nullable=True)  # Job title
    department = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)

    # Status
    is_active = Column(Boolean, default=True)
    invited_at = Column(DateTime(timezone=True), nullable=True)
    joined_at = Column(DateTime(timezone=True), nullable=True)

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")
    user = relationship("User")

    def __repr__(self):
        return f"<CompanyMember(company_id={self.company_id}, user_id={self.user_id}, role='{self.role}')>"


class CompanySettings(Base):
    """
    Detailed company settings and preferences.
    """

    __tablename__ = "company_settings"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Communication Settings
    default_agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    auto_assign_calls = Column(Boolean, default=True)
    call_recording_enabled = Column(Boolean, default=False)

    # Booking Settings
    booking_enabled = Column(Boolean, default=True)
    require_approval = Column(Boolean, default=False)
    advance_booking_days = Column(Integer, default=30)
    booking_buffer_minutes = Column(Integer, default=15)

    # Notification Settings
    email_notifications = Column(Boolean, default=True)
    sms_notifications = Column(Boolean, default=False)
    webhook_url = Column(String(500), nullable=True)

    # Business Rules
    business_hours_enforcement = Column(Boolean, default=True)
    holiday_schedule = Column(JSON, nullable=True)
    emergency_contact = Column(JSON, nullable=True)

    # Integration Settings
    google_calendar_enabled = Column(Boolean, default=False)
    google_calendar_id = Column(String(255), nullable=True)
    stripe_enabled = Column(Boolean, default=False)
    stripe_account_id = Column(String(255), nullable=True)

    # Custom Fields
    custom_fields = Column(JSON, nullable=True, default=dict)

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")
    default_agent = relationship("Agent")

    def __repr__(self):
        return f"<CompanySettings(company_id={self.company_id})>"


class CompanyAnalytics(Base):
    """
    Company analytics and performance metrics.
    """

    __tablename__ = "company_analytics"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)

    # Time Period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(
        String(20), nullable=False, default="daily"
    )  # hourly, daily, weekly, monthly

    # Call Metrics
    total_calls = Column(Integer, default=0)
    answered_calls = Column(Integer, default=0)
    missed_calls = Column(Integer, default=0)
    average_call_duration = Column(Integer, default=0)  # in seconds

    # Booking Metrics
    total_bookings = Column(Integer, default=0)
    confirmed_bookings = Column(Integer, default=0)
    cancelled_bookings = Column(Integer, default=0)
    booking_conversion_rate = Column(Integer, default=0)  # percentage

    # Customer Metrics
    new_customers = Column(Integer, default=0)
    returning_customers = Column(Integer, default=0)
    customer_satisfaction = Column(Integer, nullable=True)  # 1-5 rating

    # Revenue Metrics (if applicable)
    total_revenue = Column(Integer, default=0)  # in cents
    average_job_value = Column(Integer, default=0)  # in cents

    # Agent Performance
    agent_utilization = Column(Integer, default=0)  # percentage
    response_time_avg = Column(Integer, default=0)  # in seconds

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    company = relationship("Company")

    def __repr__(self):
        return f"<CompanyAnalytics(company_id={self.company_id}, date='{self.date}')>"
