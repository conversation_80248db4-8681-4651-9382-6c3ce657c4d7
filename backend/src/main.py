import json
import logging
import socket
import time
import uuid
from contextlib import asynccontextmanager

import redis.asyncio as aioredis
from fastapi import Fast<PERSON>I, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend as CacheRedisBackend
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

import core.db.models
from core.config import get_settings
from core.db.database import AsyncSessionLocal as SessionLocal
from core.middleware.request_context_middleware import RequestContextMiddleware
from core.middleware.request_logging_middleware import request_logging_middleware
from core.utils.logging import setup_logging
from modules.agents.router import router as agents_router
from modules.analytics.router import router as analytics_router
from modules.auth.router import router as auth_router
from modules.auth.service import create_user, get_user_by_email
from modules.bookings.router import router as bookings_router
from modules.chat_plugin.router import router as chat_plugin_router
from modules.companies.router import router as companies_router
from modules.conversations.router import router as conversations_router
from modules.customers import crud as customer_crud, schemas as customer_schemas
from modules.error.router import router as error_router
from modules.knowledge_base.router import router as knowledge_base_router
from modules.phone_numbers.router import router as phone_numbers_router
from modules.requests.router import router as requests_router
from modules.testing.router import router as testing_router
from modules.tools.router import router as tools_router
from modules.users.router import router as users_router
from modules.web_forms.router import router as web_forms_router

# Setup logging as early as possible
setup_logging()

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    settings = get_settings()
    # Startup events
    host = "localhost"
    port = settings.PORT

    try:
        network_ip = socket.gethostbyname(socket.gethostname())
    except socket.gaierror:
        network_ip = "<network_ip>"

    logger.info("\n" + "=" * 40)
    logger.info("🚀 Backend Server Started")
    logger.info(f"- Local:   http://{host}:{port}")
    logger.info(f"- Network: http://{network_ip}:{port}")
    logger.info("=" * 40 + "\n")

    async with SessionLocal() as db:
        # Create default test user if it doesn't exist
        test_user_email = "<EMAIL>"
        existing_test_user = await get_user_by_email(db, test_user_email)

        if not existing_test_user:
            logger.info("Creating default test user.")
            await create_user(
                db,
                email=test_user_email,
                password="testpassword123",
                full_name="Test User",
            )
            logger.info(f"✅ Default test user created: {test_user_email}")
        else:
            logger.info(f"Default test user already exists: {test_user_email}")

        # Check if a test customer with the domain exists, regardless of user_id
        existing_test_customer = await customer_crud.get_customer_by_domain_unfiltered(
            db, domain="test.customer"
        )

        if not existing_test_customer:
            logger.info("Creating default test customer.")
            # Create the test customer and assign it to user_id 1
            await customer_crud.create_customer(
                db,
                customer_schemas.CustomerCreate(
                    name="Test Customer", domain="test.customer"
                ),
                user_id=1,
            )
        elif existing_test_customer.user_id != 1:
            logger.warning(
                f"Test customer with domain 'test.customer' already exists "
                f"but is owned by user_id {existing_test_customer.user_id}. "
                f"Skipping creation of default test customer for user_id 1."
            )
        else:
            logger.info("Default test customer already exists for user_id 1.")

    cache_redis_client = aioredis.from_url(
        f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}",
        encoding="utf8",
        decode_responses=True,
    )
    FastAPICache.init(CacheRedisBackend(cache_redis_client), prefix="fastapi-cache")
    logger.info("FastAPICache initialized.")

    yield

    await cache_redis_client.aclose()
    logger.info("FastAPICache closed.")


app = FastAPI(lifespan=lifespan)

# Add the request context middleware
app.add_middleware(RequestContextMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8000",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request Logging Middleware
app.middleware("http")(request_logging_middleware)


app.include_router(agents_router, prefix="/api")
app.include_router(analytics_router, prefix="/api")
app.include_router(auth_router, prefix="/api")
app.include_router(bookings_router, prefix="/api")
app.include_router(chat_plugin_router, prefix="/api")
app.include_router(companies_router, prefix="/api")
app.include_router(conversations_router, prefix="/api")
app.include_router(error_router, prefix="/api")
app.include_router(knowledge_base_router, prefix="/api")
app.include_router(phone_numbers_router, prefix="/api")
app.include_router(requests_router, prefix="/api")
app.include_router(testing_router, prefix="/api")
app.include_router(tools_router, prefix="/api")
app.include_router(users_router, prefix="/api/users")
app.include_router(web_forms_router, prefix="/api")


# Log all registered routes
registered_routes = []
for route in app.routes:
    if hasattr(route, "path") and hasattr(route, "name"):
        registered_routes.append({"path": route.path, "name": route.name})
registered_routes_sorted = sorted(registered_routes, key=lambda x: x["path"])
print("Registered API Routes:\n" + json.dumps(registered_routes_sorted, indent=2))


@app.get("/")
async def read_root():
    return {"message": "Hello from Fieson backend!"}


@app.get("/health")
async def health_check():
    return {"status": "ok"}


@app.websocket("/ws")
async def websocket(websocket: WebSocket):
    await websocket.accept()
    await websocket.send_json({"msg": "Hello WebSocket"})
    try:
        while True:
            data = await websocket.receive_json()
            await websocket.send_json({"msg": f"Message text was: {data['text']}"})
    except WebSocketDisconnect:
        logger.info("Client disconnected")
