#!/usr/bin/env python3
"""
Database seeding script for Home Service Platform
Creates comprehensive test <NAME_EMAIL> user
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.core.db.database import get_db_session
from src.modules.users.models import User
from src.modules.companies.models import Company
from src.modules.agents.models import Agent
from src.modules.phone_numbers.models import PhoneNumber
from src.modules.conversations.models import Conversation
from src.modules.bookings.models import BookingRequest
from src.modules.knowledge_base.models import KnowledgeBaseItem
from src.modules.web_forms.models import (
    WebForm, WebFormField, WebFormSubmission, WebFormResponse,
    FormTemplate, AppointmentSlot, FormBooking,
    WebFormStatus, WebFieldType, WebFormSubmissionStatus
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test user configuration
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"  # This should be hashed in production

class DatabaseSeeder:
    def __init__(self):
        self.db: AsyncSession = None
        self.test_user: User = None
        self.test_company: Company = None
        
    async def __aenter__(self):
        self.db = await get_db_session().__anext__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            await self.db.close()

    async def seed_all(self):
        """Seed all data for test user"""
        logger.info("🌱 Starting database seeding...")
        
        try:
            # Create test user and company
            await self.create_test_user()
            await self.create_test_company()
            
            # Create core business data
            await self.create_agents()
            await self.create_phone_numbers()
            await self.create_knowledge_base()
            
            # Create form templates
            await self.create_form_templates()
            
            # Create web forms
            await self.create_web_forms()
            
            # Create appointment slots
            await self.create_appointment_slots()
            
            # Create conversations
            await self.create_conversations()
            
            # Create bookings
            await self.create_bookings()
            
            # Create form submissions
            await self.create_form_submissions()
            
            await self.db.commit()
            logger.info("✅ Database seeding completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Error during seeding: {e}")
            await self.db.rollback()
            raise

    async def create_test_user(self):
        """Create test user"""
        # Check if user already exists
        result = await self.db.execute(
            select(User).where(User.email == TEST_USER_EMAIL)
        )
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            self.test_user = existing_user
            logger.info(f"📧 Using existing test user: {TEST_USER_EMAIL}")
            return
            
        # Create new user
        from src.modules.auth.service import get_password_hash
        
        self.test_user = User(
            email=TEST_USER_EMAIL,
            hashed_password=get_password_hash(TEST_USER_PASSWORD),
            full_name="Test User",
            is_active=True,
            is_verified=True,
        )
        
        self.db.add(self.test_user)
        await self.db.flush()
        logger.info(f"👤 Created test user: {TEST_USER_EMAIL}")

    async def create_test_company(self):
        """Create test company"""
        self.test_company = Company(
            name="Elite Home Services",
            description="Professional home maintenance and repair services",
            industry="Home Services",
            phone="+****************",
            email="<EMAIL>",
            website="https://elitehomeservices.com",
            address="123 Main St, Anytown, ST 12345",
            owner_id=self.test_user.id,
        )
        
        self.db.add(self.test_company)
        await self.db.flush()
        logger.info(f"🏢 Created test company: {self.test_company.name}")

    async def create_agents(self):
        """Create test agents"""
        agents_data = [
            {
                "name": "Sarah Johnson",
                "email": "<EMAIL>",
                "phone": "+****************",
                "role": "Senior Technician",
                "specialties": ["Plumbing", "HVAC"],
                "status": "active",
            },
            {
                "name": "Mike Wilson", 
                "email": "<EMAIL>",
                "phone": "+****************",
                "role": "Electrical Specialist",
                "specialties": ["Electrical"],
                "status": "active",
            },
        ]
        
        for agent_data in agents_data:
            agent = Agent(
                company_id=self.test_company.id,
                **agent_data
            )
            self.db.add(agent)
            
        await self.db.flush()
        logger.info(f"👥 Created {len(agents_data)} agents")

    async def create_phone_numbers(self):
        """Create test phone numbers"""
        # Get agents
        result = await self.db.execute(
            select(Agent).where(Agent.company_id == self.test_company.id)
        )
        agents = result.scalars().all()
        
        phone_numbers_data = [
            {
                "number": "+****************",
                "type": "main",
                "agent_id": agents[0].id if agents else None,
                "status": "active",
            },
            {
                "number": "+****************",
                "type": "emergency", 
                "agent_id": agents[1].id if len(agents) > 1 else None,
                "status": "active",
            },
        ]
        
        for phone_data in phone_numbers_data:
            phone = PhoneNumber(
                company_id=self.test_company.id,
                **phone_data
            )
            self.db.add(phone)
            
        await self.db.flush()
        logger.info(f"📞 Created {len(phone_numbers_data)} phone numbers")

    async def create_knowledge_base(self):
        """Create knowledge base items"""
        kb_items = [
            {
                "title": "Common Plumbing Issues",
                "content": "Guide to diagnosing and fixing common plumbing problems including leaky faucets, clogged drains, and running toilets.",
                "category": "Plumbing",
                "tags": ["plumbing", "troubleshooting", "repair"],
            },
            {
                "title": "HVAC Maintenance Schedule",
                "content": "Recommended maintenance schedule for HVAC systems including filter changes, coil cleaning, and seasonal inspections.",
                "category": "HVAC",
                "tags": ["hvac", "maintenance", "schedule"],
            },
            {
                "title": "Electrical Safety Guidelines",
                "content": "Important safety guidelines for electrical work including circuit breaker identification and basic troubleshooting.",
                "category": "Electrical",
                "tags": ["electrical", "safety", "guidelines"],
            },
        ]
        
        for kb_data in kb_items:
            kb_item = KnowledgeBaseItem(
                company_id=self.test_company.id,
                **kb_data
            )
            self.db.add(kb_item)
            
        await self.db.flush()
        logger.info(f"📚 Created {len(kb_items)} knowledge base items")

    async def create_form_templates(self):
        """Create form templates"""
        templates_data = [
            {
                "name": "Service Request Form",
                "description": "General service request form for home services",
                "category": "service_request",
                "template_data": {
                    "pages": [
                        {
                            "id": "contact_info",
                            "title": "Contact Information",
                            "fields": [
                                {"id": "name", "type": "short_text", "title": "Full Name", "required": True},
                                {"id": "email", "type": "email", "title": "Email Address", "required": True},
                                {"id": "phone", "type": "phone", "title": "Phone Number", "required": True},
                            ]
                        },
                        {
                            "id": "service_details",
                            "title": "Service Details",
                            "fields": [
                                {"id": "service_type", "type": "service_type", "title": "Service Type", "required": True},
                                {"id": "description", "type": "long_text", "title": "Description", "required": True},
                                {"id": "address", "type": "address", "title": "Service Address", "required": True},
                            ]
                        }
                    ]
                },
                "is_public": True,
                "tags": ["service", "request", "general"],
                "usage_count": 0,
                "rating": 4.8,
            },
            {
                "name": "Plumbing Emergency Form",
                "description": "Emergency plumbing service request form",
                "category": "emergency",
                "template_data": {
                    "pages": [
                        {
                            "id": "emergency_contact",
                            "title": "Emergency Contact",
                            "fields": [
                                {"id": "name", "type": "short_text", "title": "Full Name", "required": True},
                                {"id": "phone", "type": "phone", "title": "Phone Number", "required": True},
                                {"id": "address", "type": "address", "title": "Emergency Address", "required": True},
                                {"id": "urgency", "type": "dropdown", "title": "Urgency Level", "required": True, 
                                 "options": ["Low", "Medium", "High", "Emergency"]},
                            ]
                        }
                    ]
                },
                "is_public": True,
                "tags": ["plumbing", "emergency", "urgent"],
                "usage_count": 0,
                "rating": 4.9,
            },
        ]
        
        for template_data in templates_data:
            template = FormTemplate(
                company_id=self.test_company.id,
                **template_data
            )
            self.db.add(template)
            
        await self.db.flush()
        logger.info(f"📋 Created {len(templates_data)} form templates")

    async def create_web_forms(self):
        """Create web forms"""
        forms_data = [
            {
                "web_form_id": "service-request-001",
                "title": "General Service Request",
                "description": "Request any home service",
                "status": WebFormStatus.PUBLISHED.value,
                "form_type": "service_request",
                "is_multi_step": True,
                "show_progress_bar": True,
                "allow_multiple_submissions": True,
                "thank_you_message": "Thank you for your service request! We'll contact you soon.",
                "fields": [
                    {
                        "field_id": "customer_name",
                        "title": "Full Name",
                        "field_type": WebFieldType.SHORT_TEXT.value,
                        "is_required": True,
                        "order_index": 1,
                        "placeholder_text": "Enter your full name",
                    },
                    {
                        "field_id": "customer_email",
                        "title": "Email Address",
                        "field_type": WebFieldType.EMAIL.value,
                        "is_required": True,
                        "order_index": 2,
                        "placeholder_text": "Enter your email address",
                    },
                    {
                        "field_id": "customer_phone",
                        "title": "Phone Number",
                        "field_type": WebFieldType.PHONE.value,
                        "is_required": True,
                        "order_index": 3,
                        "placeholder_text": "Enter your phone number",
                    },
                    {
                        "field_id": "service_type",
                        "title": "Service Type",
                        "field_type": WebFieldType.SERVICE_TYPE.value,
                        "is_required": True,
                        "order_index": 4,
                        "options": ["Plumbing", "HVAC", "Electrical", "Roofing", "Other"],
                    },
                    {
                        "field_id": "service_description",
                        "title": "Service Description",
                        "field_type": WebFieldType.LONG_TEXT.value,
                        "is_required": True,
                        "order_index": 5,
                        "placeholder_text": "Describe the service you need",
                    },
                ]
            },
        ]
        
        for form_data in forms_data:
            fields_data = form_data.pop("fields", [])
            
            web_form = WebForm(
                company_id=self.test_company.id,
                user_id=self.test_user.id,
                **form_data
            )
            self.db.add(web_form)
            await self.db.flush()
            
            # Add fields
            for field_data in fields_data:
                field = WebFormField(
                    form_id=web_form.id,
                    **field_data
                )
                self.db.add(field)
                
        await self.db.flush()
        logger.info(f"📝 Created {len(forms_data)} web forms")

    async def create_appointment_slots(self):
        """Create appointment slots"""
        # Create slots for next 7 days
        base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        
        slots = []
        for day in range(7):
            date = base_date + timedelta(days=day)
            # Create 8 slots per day (9 AM to 5 PM)
            for hour in range(8):
                start_time = date + timedelta(hours=hour)
                end_time = start_time + timedelta(hours=1)
                
                slot = AppointmentSlot(
                    company_id=self.test_company.id,
                    date=date.date(),
                    start_time=start_time,
                    end_time=end_time,
                    duration_minutes=60,
                    is_available=True,
                    max_bookings=1,
                    current_bookings=0,
                    service_types=["Plumbing", "HVAC", "Electrical"],
                    base_price=150.0,
                    dispatch_fee=50.0,
                    service_areas=["12345", "67890", "54321"],
                )
                slots.append(slot)
                
        for slot in slots:
            self.db.add(slot)
            
        await self.db.flush()
        logger.info(f"📅 Created {len(slots)} appointment slots")

    async def create_conversations(self):
        """Create sample conversations"""
        conversations_data = [
            {
                "customer_name": "John Smith",
                "customer_phone": "+****************",
                "customer_email": "<EMAIL>",
                "channel": "phone",
                "status": "completed",
                "started_at": datetime.now() - timedelta(days=2),
                "ended_at": datetime.now() - timedelta(days=2, hours=-1),
                "duration_seconds": 3600,
                "message_count": 12,
                "customer_satisfaction": 5,
                "outcome": "service_scheduled",
                "summary": "Customer reported leaky faucet. Scheduled repair appointment.",
            },
            {
                "customer_name": "Mary Johnson",
                "customer_phone": "+****************", 
                "customer_email": "<EMAIL>",
                "channel": "web",
                "status": "completed",
                "started_at": datetime.now() - timedelta(days=1),
                "ended_at": datetime.now() - timedelta(days=1, minutes=-30),
                "duration_seconds": 1800,
                "message_count": 8,
                "customer_satisfaction": 4,
                "outcome": "quote_provided",
                "summary": "Customer inquired about electrical outlet installation.",
            },
        ]
        
        for conv_data in conversations_data:
            conversation = Conversation(
                company_id=self.test_company.id,
                **conv_data
            )
            self.db.add(conversation)
            
        await self.db.flush()
        logger.info(f"💬 Created {len(conversations_data)} conversations")

    async def create_bookings(self):
        """Create sample bookings"""
        bookings_data = [
            {
                "customer_name": "John Smith",
                "customer_phone": "+****************",
                "customer_email": "<EMAIL>",
                "service_type": "Plumbing",
                "service_category": "Repair",
                "description": "Fix leaky kitchen faucet",
                "scheduled_date": datetime.now() + timedelta(days=1),
                "status": "confirmed",
                "service_address": "456 Oak St, Anytown, ST 12345",
                "estimated_price": 150.0,
                "dispatch_fee": 50.0,
                "total_amount": 200.0,
            },
            {
                "customer_name": "Mary Johnson",
                "customer_phone": "+****************",
                "customer_email": "<EMAIL>", 
                "service_type": "Electrical",
                "service_category": "Installation",
                "description": "Install new electrical outlets in home office",
                "scheduled_date": datetime.now() + timedelta(days=2),
                "status": "pending",
                "service_address": "789 Pine St, Anytown, ST 12345",
                "estimated_price": 300.0,
                "dispatch_fee": 50.0,
                "total_amount": 350.0,
            },
        ]
        
        for booking_data in bookings_data:
            booking = BookingRequest(
                company_id=self.test_company.id,
                **booking_data
            )
            self.db.add(booking)
            
        await self.db.flush()
        logger.info(f"📋 Created {len(bookings_data)} bookings")

    async def create_form_submissions(self):
        """Create sample form submissions"""
        # Get web forms
        result = await self.db.execute(
            select(WebForm).where(WebForm.company_id == self.test_company.id)
        )
        web_forms = result.scalars().all()
        
        if not web_forms:
            logger.warning("No web forms found, skipping submissions")
            return
            
        submissions_data = [
            {
                "submission_id": "sub_001",
                "contact_name": "Alice Brown",
                "contact_email": "<EMAIL>",
                "contact_phone": "+****************",
                "status": WebFormSubmissionStatus.COMPLETED.value,
                "completion_percentage": 100.0,
                "started_at": datetime.now() - timedelta(hours=2),
                "completed_at": datetime.now() - timedelta(hours=1, minutes=45),
                "time_to_complete": 900,  # 15 minutes
                "responses": [
                    {"field_id": "customer_name", "response_value": "Alice Brown"},
                    {"field_id": "customer_email", "response_value": "<EMAIL>"},
                    {"field_id": "customer_phone", "response_value": "+****************"},
                    {"field_id": "service_type", "response_value": "Plumbing"},
                    {"field_id": "service_description", "response_value": "Bathroom sink is clogged"},
                ]
            },
            {
                "submission_id": "sub_002",
                "contact_name": "Bob Wilson",
                "contact_email": "<EMAIL>",
                "contact_phone": "+****************",
                "status": WebFormSubmissionStatus.PENDING.value,
                "completion_percentage": 100.0,
                "started_at": datetime.now() - timedelta(hours=4),
                "completed_at": datetime.now() - timedelta(hours=3, minutes=30),
                "time_to_complete": 1800,  # 30 minutes
                "responses": [
                    {"field_id": "customer_name", "response_value": "Bob Wilson"},
                    {"field_id": "customer_email", "response_value": "<EMAIL>"},
                    {"field_id": "customer_phone", "response_value": "+****************"},
                    {"field_id": "service_type", "response_value": "HVAC"},
                    {"field_id": "service_description", "response_value": "Air conditioning not cooling properly"},
                ]
            },
        ]
        
        for submission_data in submissions_data:
            responses_data = submission_data.pop("responses", [])
            
            submission = WebFormSubmission(
                web_form_id=web_forms[0].id,
                company_id=self.test_company.id,
                **submission_data
            )
            self.db.add(submission)
            await self.db.flush()
            
            # Get form fields for responses
            result = await self.db.execute(
                select(WebFormField).where(WebFormField.form_id == web_forms[0].id)
            )
            form_fields = {field.field_id: field for field in result.scalars().all()}
            
            # Add responses
            for response_data in responses_data:
                field_id = response_data["field_id"]
                if field_id in form_fields:
                    response = WebFormResponse(
                        submission_id=submission.id,
                        field_id=form_fields[field_id].id,
                        response_value=response_data["response_value"],
                        response_time=30,  # 30 seconds per field
                    )
                    self.db.add(response)
                    
        await self.db.flush()
        logger.info(f"📊 Created {len(submissions_data)} form submissions")


async def main():
    """Main seeding function"""
    async with DatabaseSeeder() as seeder:
        await seeder.seed_all()


if __name__ == "__main__":
    asyncio.run(main())
