{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-query": "^5.85.3", "axios": "^1.7.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "framer-motion": "^12.23.12", "lucide-react": "^0.518.0", "next": "15.4.6", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.9.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "react-resizable-panels": "^3.0.5", "recharts": "^2.15.4", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "tailwind-merge": "^2.6.0", "vaul": "^1.1.2", "zod": "^4.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "prettier": "^3.6.2", "shadcn": "^3.0.0", "tailwindcss": "^4.1.10", "typescript": "^5"}}