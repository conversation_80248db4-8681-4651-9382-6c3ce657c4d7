import { type FormTemplate } from './services';

export const comprehensiveTemplates: FormTemplate[] = [
  // ROOFING TEMPLATES
  {
    id: 'roofing-inspection',
    name: 'Roofing Inspection Request',
    description: 'Professional roof inspection and assessment form',
    category: 'roofing',
    preview_image: '/templates/roofing-inspection.png',
    tags: ['roofing', 'inspection', 'assessment', 'damage'],
    usage_count: 189,
    rating: 4.9,
    pages: [
      {
        id: 'location',
        title: 'Property Location',
        description: 'Where is your property located?',
        fields: [
          {
            id: 'zip_code',
            type: 'zip_code',
            label: 'ZIP Code',
            placeholder: 'Enter your ZIP code',
            required: true,
            width: 'half',
          },
        ],
      },
      {
        id: 'contact',
        title: 'Contact Information',
        description: 'How can we reach you?',
        fields: [
          {
            id: 'customer_name',
            type: 'short_text',
            label: 'Full Name',
            placeholder: 'Enter your full name',
            required: true,
          },
          {
            id: 'phone',
            type: 'phone',
            label: 'Phone Number',
            required: true,
            width: 'half',
          },
          {
            id: 'email',
            type: 'email',
            label: 'Email Address',
            required: true,
            width: 'half',
          },
        ],
      },
      {
        id: 'property_details',
        title: 'Property Details',
        description: 'Tell us about your roof',
        fields: [
          {
            id: 'property_address',
            type: 'address',
            label: 'Property Address',
            required: true,
          },
          {
            id: 'roof_type',
            type: 'multiple_choice',
            label: 'What type of roof do you have?',
            options: ['Asphalt Shingles', 'Metal', 'Tile', 'Slate', 'Flat/TPO', 'Not Sure'],
            required: true,
          },
          {
            id: 'roof_age',
            type: 'multiple_choice',
            label: 'How old is your roof?',
            options: ['Less than 5 years', '5-10 years', '10-20 years', '20+ years', 'Not Sure'],
            required: false,
          },
          {
            id: 'issue_description',
            type: 'long_text',
            label: 'Describe any issues or concerns',
            placeholder: 'Tell us about leaks, missing shingles, storm damage, etc.',
            required: false,
          },
        ],
      },
      {
        id: 'inspection_type',
        title: 'Inspection Details',
        description: 'What type of inspection do you need?',
        fields: [
          {
            id: 'inspection_reason',
            type: 'multiple_choice',
            label: 'Reason for inspection',
            options: [
              'Storm damage assessment',
              'Insurance claim',
              'Pre-purchase inspection',
              'General maintenance',
              'Leak investigation',
              'Other',
            ],
            required: true,
          },
          {
            id: 'urgency',
            type: 'multiple_choice',
            label: 'How urgent is this?',
            options: ['Emergency (Active leak)', 'Within a week', 'Within a month', 'No rush'],
            required: true,
          },
          {
            id: 'photos',
            type: 'file_upload',
            label: 'Upload photos of damage (optional)',
            help_text: 'Photos help us prepare for the inspection',
            required: false,
          },
        ],
      },
      {
        id: 'appointment',
        title: 'Schedule Inspection',
        description: 'When would you like us to inspect your roof?',
        fields: [
          {
            id: 'appointment_slots',
            type: 'appointment_slots',
            label: 'Available Inspection Times',
            required: true,
          },
        ],
      },
    ],
    settings: {
      navigation_type: 'vertical',
      show_progress_bar: true,
      success_message: "Thank you! We'll inspect your roof and provide a detailed report.",
      enable_booking: true,
      show_pricing: true,
      dispatch_fee: 0,
    },
  },

  // HVAC TEMPLATES
  {
    id: 'hvac-service-call',
    name: 'HVAC Service Request',
    description: 'Heating and cooling system service and repair',
    category: 'hvac',
    preview_image: '/templates/hvac-service.png',
    tags: ['hvac', 'heating', 'cooling', 'repair', 'maintenance'],
    usage_count: 312,
    rating: 4.7,
    pages: [
      {
        id: 'location',
        title: 'Service Location',
        fields: [
          {
            id: 'zip_code',
            type: 'zip_code',
            label: 'ZIP Code',
            required: true,
            width: 'full',
          },
        ],
      },
      {
        id: 'contact',
        title: 'Contact Information',
        fields: [
          {
            id: 'customer_name',
            type: 'short_text',
            label: 'Full Name',
            required: true,
          },
          {
            id: 'phone',
            type: 'phone',
            label: 'Phone Number',
            required: true,
            width: 'half',
          },
          {
            id: 'email',
            type: 'email',
            label: 'Email Address',
            required: true,
            width: 'half',
          },
        ],
      },
      {
        id: 'system_details',
        title: 'HVAC System Details',
        fields: [
          {
            id: 'service_address',
            type: 'address',
            label: 'Service Address',
            required: true,
          },
          {
            id: 'system_type',
            type: 'multiple_choice',
            label: 'What type of system needs service?',
            options: [
              'Central Air/Heat Pump',
              'Furnace',
              'Boiler',
              'Ductless Mini-Split',
              'Window Unit',
              'Not Sure',
            ],
            required: true,
          },
          {
            id: 'system_age',
            type: 'multiple_choice',
            label: 'How old is your system?',
            options: ['Less than 5 years', '5-10 years', '10-15 years', '15+ years', 'Not Sure'],
            required: false,
          },
          {
            id: 'issue_description',
            type: 'long_text',
            label: 'Describe the problem',
            placeholder: 'No heat, no cooling, strange noises, high bills, etc.',
            required: true,
          },
        ],
      },
      {
        id: 'service_type',
        title: 'Service Needed',
        fields: [
          {
            id: 'service_category',
            type: 'multiple_choice',
            label: 'What type of service do you need?',
            options: [
              'Emergency Repair',
              'Routine Maintenance',
              'System Installation',
              'Duct Cleaning',
              'Thermostat Service',
              'Other',
            ],
            required: true,
          },
          {
            id: 'appointment_slots',
            type: 'appointment_slots',
            label: 'Preferred Service Time',
            required: true,
          },
        ],
      },
    ],
    settings: {
      navigation_type: 'vertical',
      show_progress_bar: true,
      success_message: 'Service request received! Our HVAC technician will contact you shortly.',
      enable_booking: true,
      show_pricing: true,
      dispatch_fee: 89,
    },
  },

  // PLUMBING TEMPLATES
  {
    id: 'plumbing-emergency',
    name: 'Emergency Plumbing Service',
    description: '24/7 emergency plumbing repairs and service',
    category: 'plumbing',
    preview_image: '/templates/plumbing-emergency.png',
    tags: ['plumbing', 'emergency', 'leak', 'repair', '24/7'],
    usage_count: 456,
    rating: 4.8,
    pages: [
      {
        id: 'emergency_details',
        title: 'Emergency Plumbing Service',
        description: "We're here to help with your plumbing emergency",
        fields: [
          {
            id: 'emergency_type',
            type: 'multiple_choice',
            label: "What's the emergency?",
            options: [
              'Burst pipe/major leak',
              'No water',
              'Sewage backup',
              'Water heater failure',
              'Toilet overflow',
              'Other emergency',
            ],
            required: true,
          },
          {
            id: 'customer_name',
            type: 'short_text',
            label: 'Your Name',
            required: true,
            width: 'half',
          },
          {
            id: 'phone',
            type: 'phone',
            label: 'Phone Number',
            required: true,
            width: 'half',
          },
          {
            id: 'emergency_description',
            type: 'long_text',
            label: 'Describe the situation',
            placeholder: 'Please provide details about the emergency...',
            required: true,
          },
        ],
      },
      {
        id: 'location',
        title: 'Service Location',
        fields: [
          {
            id: 'service_address',
            type: 'address',
            label: 'Emergency Location',
            required: true,
          },
          {
            id: 'access_instructions',
            type: 'long_text',
            label: 'Access Instructions',
            placeholder: 'How can our plumber access the property? Gate codes, key location, etc.',
            required: false,
          },
        ],
      },
    ],
    settings: {
      navigation_type: 'vertical',
      show_progress_bar: false,
      success_message: 'Emergency request received! A plumber will contact you within 15 minutes.',
      enable_booking: true,
      show_pricing: true,
      dispatch_fee: 150,
    },
  },

  // ELECTRICAL TEMPLATES
  {
    id: 'electrical-service',
    name: 'Electrical Service Request',
    description: 'Professional electrical repairs and installations',
    category: 'electrical',
    preview_image: '/templates/electrical-service.png',
    tags: ['electrical', 'wiring', 'outlets', 'panel', 'safety'],
    usage_count: 234,
    rating: 4.6,
    pages: [
      {
        id: 'location',
        title: 'Service Location',
        fields: [
          {
            id: 'zip_code',
            type: 'zip_code',
            label: 'ZIP Code',
            required: true,
            width: 'half',
          },
        ],
      },
      {
        id: 'contact',
        title: 'Contact Information',
        fields: [
          {
            id: 'customer_name',
            type: 'short_text',
            label: 'Full Name',
            required: true,
          },
          {
            id: 'phone',
            type: 'phone',
            label: 'Phone Number',
            required: true,
            width: 'half',
          },
          {
            id: 'email',
            type: 'email',
            label: 'Email Address',
            required: true,
            width: 'half',
          },
        ],
      },
      {
        id: 'electrical_details',
        title: 'Electrical Service Details',
        fields: [
          {
            id: 'service_address',
            type: 'address',
            label: 'Service Address',
            required: true,
          },
          {
            id: 'electrical_issue',
            type: 'multiple_choice',
            label: 'What electrical service do you need?',
            options: [
              'Outlet/Switch Repair',
              'Panel Upgrade',
              'New Installation',
              'Lighting Issues',
              'Wiring Problems',
              'Safety Inspection',
              'Other',
            ],
            required: true,
          },
          {
            id: 'urgency',
            type: 'multiple_choice',
            label: 'How urgent is this?',
            options: [
              'Emergency (No power/Safety hazard)',
              'Within a week',
              'Within a month',
              'No rush',
            ],
            required: true,
          },
          {
            id: 'issue_description',
            type: 'long_text',
            label: 'Describe the electrical issue',
            placeholder: 'Please provide details about the electrical problem...',
            required: true,
          },
        ],
      },
      {
        id: 'appointment',
        title: 'Schedule Service',
        fields: [
          {
            id: 'appointment_slots',
            type: 'appointment_slots',
            label: 'Available Service Times',
            required: true,
          },
        ],
      },
    ],
    settings: {
      navigation_type: 'vertical',
      show_progress_bar: true,
      success_message:
        'Electrical service request received! Our licensed electrician will contact you shortly.',
      enable_booking: true,
      show_pricing: true,
      dispatch_fee: 95,
    },
  },

  // LANDSCAPING TEMPLATES
  {
    id: 'landscaping-service',
    name: 'Landscaping Service Request',
    description: 'Professional landscaping and lawn care services',
    category: 'landscaping',
    preview_image: '/templates/landscaping-service.png',
    tags: ['landscaping', 'lawn care', 'garden', 'maintenance', 'design'],
    usage_count: 167,
    rating: 4.5,
    pages: [
      {
        id: 'location',
        title: 'Property Location',
        fields: [
          {
            id: 'zip_code',
            type: 'zip_code',
            label: 'ZIP Code',
            required: true,
            width: 'half',
          },
        ],
      },
      {
        id: 'contact',
        title: 'Contact Information',
        fields: [
          {
            id: 'customer_name',
            type: 'short_text',
            label: 'Full Name',
            required: true,
          },
          {
            id: 'phone',
            type: 'phone',
            label: 'Phone Number',
            required: true,
            width: 'half',
          },
          {
            id: 'email',
            type: 'email',
            label: 'Email Address',
            required: true,
            width: 'half',
          },
        ],
      },
      {
        id: 'property_details',
        title: 'Property & Service Details',
        fields: [
          {
            id: 'service_address',
            type: 'address',
            label: 'Property Address',
            required: true,
          },
          {
            id: 'property_type',
            type: 'multiple_choice',
            label: 'Property type',
            options: [
              'Residential Home',
              'Commercial Property',
              'Apartment Complex',
              'HOA Community',
            ],
            required: true,
          },
          {
            id: 'service_type',
            type: 'multiple_choice',
            label: 'What landscaping service do you need?',
            options: [
              'Lawn Maintenance',
              'Garden Design',
              'Tree/Shrub Care',
              'Irrigation System',
              'Hardscaping',
              'Seasonal Cleanup',
              'Other',
            ],
            required: true,
          },
          {
            id: 'property_size',
            type: 'multiple_choice',
            label: 'Approximate property size',
            options: [
              'Small (under 1/4 acre)',
              'Medium (1/4 - 1/2 acre)',
              'Large (1/2 - 1 acre)',
              'Very Large (over 1 acre)',
              'Not Sure',
            ],
            required: false,
          },
          {
            id: 'service_description',
            type: 'long_text',
            label: 'Describe your landscaping needs',
            placeholder: 'Tell us about your vision, current issues, or specific requirements...',
            required: true,
          },
        ],
      },
      {
        id: 'scheduling',
        title: 'Service Scheduling',
        fields: [
          {
            id: 'service_frequency',
            type: 'multiple_choice',
            label: 'How often do you need service?',
            options: [
              'One-time service',
              'Weekly',
              'Bi-weekly',
              'Monthly',
              'Seasonal',
              'As needed',
            ],
            required: true,
          },
          {
            id: 'appointment_slots',
            type: 'appointment_slots',
            label: 'Preferred Consultation Time',
            required: true,
          },
        ],
      },
    ],
    settings: {
      navigation_type: 'vertical',
      show_progress_bar: true,
      success_message: 'Thank you! Our landscaping team will contact you to discuss your project.',
      enable_booking: true,
      show_pricing: false,
      dispatch_fee: 0,
    },
  },

  // PEST CONTROL TEMPLATES
  {
    id: 'pest-control-service',
    name: 'Pest Control Service Request',
    description: 'Professional pest control and extermination services',
    category: 'pest_control',
    preview_image: '/templates/pest-control-service.png',
    tags: ['pest control', 'extermination', 'insects', 'rodents', 'treatment'],
    usage_count: 298,
    rating: 4.7,
    pages: [
      {
        id: 'location',
        title: 'Service Location',
        fields: [
          {
            id: 'zip_code',
            type: 'zip_code',
            label: 'ZIP Code',
            required: true,
            width: 'half',
          },
        ],
      },
      {
        id: 'contact',
        title: 'Contact Information',
        fields: [
          {
            id: 'customer_name',
            type: 'short_text',
            label: 'Full Name',
            required: true,
          },
          {
            id: 'phone',
            type: 'phone',
            label: 'Phone Number',
            required: true,
            width: 'half',
          },
          {
            id: 'email',
            type: 'email',
            label: 'Email Address',
            required: true,
            width: 'half',
          },
        ],
      },
      {
        id: 'pest_details',
        title: 'Pest Problem Details',
        fields: [
          {
            id: 'service_address',
            type: 'address',
            label: 'Service Address',
            required: true,
          },
          {
            id: 'pest_type',
            type: 'multiple_choice',
            label: 'What type of pest problem do you have?',
            options: [
              'Ants',
              'Cockroaches',
              'Spiders',
              'Rodents (Mice/Rats)',
              'Termites',
              'Bed Bugs',
              'Wasps/Bees',
              'Other/Multiple',
            ],
            required: true,
          },
          {
            id: 'severity',
            type: 'multiple_choice',
            label: 'How severe is the problem?',
            options: [
              'Just noticed a few',
              'Moderate infestation',
              'Severe infestation',
              'Emergency situation',
            ],
            required: true,
          },
          {
            id: 'location_details',
            type: 'multiple_choice',
            label: 'Where have you seen the pests?',
            options: [
              'Kitchen',
              'Bathroom',
              'Basement',
              'Attic',
              'Living areas',
              'Outdoor areas',
              'Multiple locations',
            ],
            required: true,
          },
          {
            id: 'problem_description',
            type: 'long_text',
            label: 'Describe the pest problem',
            placeholder:
              'When did you first notice? How many have you seen? Any specific areas of concern?',
            required: true,
          },
        ],
      },
      {
        id: 'service_preferences',
        title: 'Service Preferences',
        fields: [
          {
            id: 'treatment_preference',
            type: 'multiple_choice',
            label: 'Treatment preference',
            options: [
              'Eco-friendly/Green options',
              'Standard treatment',
              'Most effective (any method)',
              'No preference',
            ],
            required: false,
          },
          {
            id: 'pets_children',
            type: 'yes_no',
            label: 'Do you have pets or small children?',
            required: true,
          },
          {
            id: 'appointment_slots',
            type: 'appointment_slots',
            label: 'Preferred Service Time',
            required: true,
          },
        ],
      },
    ],
    settings: {
      navigation_type: 'vertical',
      show_progress_bar: true,
      success_message:
        'Pest control request received! Our certified technician will contact you shortly.',
      enable_booking: true,
      show_pricing: true,
      dispatch_fee: 75,
    },
  },
];
