import {
  Drop<PERSON>,
  <PERSON>f<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Plus<PERSON>quare,
  <PERSON>fresh<PERSON><PERSON><PERSON>,
  <PERSON>lip<PERSON>,
  type LucideIcon,
} from 'lucide-react';

export interface ServiceOption {
  id: string;
  name: string;
  description?: string;
}

export interface ServiceSubCategory {
  id: string;
  name: string;
  icon: LucideIcon;
  options: ServiceOption[];
}

export interface ServiceCategory {
  id: string;
  name: string;
  icon: LucideIcon;
  description: string;
  color: string;
  subCategories: ServiceSubCategory[];
}

export const services: ServiceCategory[] = [
  {
    id: 'plumbing',
    name: 'Plumbing',
    icon: Droplet,
    description: 'Repairs, installations, and maintenance',
    color: 'text-blue-600',
    subCategories: [
      {
        id: 'repair',
        name: 'Repair',
        icon: Wrench,
        options: [
          { id: 'find-repair-leak', name: 'Find & repair leak' },
          { id: 'repair-faucet', name: 'Repair faucet' },
          { id: 'repair-garbage-disposal', name: 'Repair garbage disposal' },
          { id: 'repair-outdoor-systems', name: 'Repair outdoor systems' },
          { id: 'repair-pipe', name: 'Repair pipe' },
          { id: 'repair-sewer', name: 'Repair sewer' },
          { id: 'repair-shower', name: 'Repair shower' },
          { id: 'repair-toilet', name: 'Repair toilet' },
          { id: 'repair-water-heater', name: 'Repair water heater' },
          { id: 'unclog-drain', name: 'Unclog drain' },
        ],
      },
      {
        id: 'install',
        name: 'Install',
        icon: PlusSquare,
        options: [
          { id: 'install-faucet', name: 'Install faucet' },
          { id: 'install-garbage-disposal', name: 'Install garbage disposal' },
          { id: 'install-shower', name: 'Install shower' },
          { id: 'install-toilet', name: 'Install toilet' },
          { id: 'install-water-heater', name: 'Install water heater' },
        ],
      },
      {
        id: 'maintenance',
        name: 'Maintenance',
        icon: RefreshCcw,
        options: [
          { id: 'plumbing-inspection', name: 'Plumbing inspection' },
          { id: 'drain-cleaning', name: 'Drain cleaning' },
          { id: 'water-heater-maintenance', name: 'Water heater maintenance' },
        ],
      },
      {
        id: 'other-plumbing',
        name: 'Other',
        icon: Ellipsis,
        options: [],
      },
    ],
  },
  {
    id: 'heating-cooling',
    name: 'Heating & Cooling',
    icon: Snowflake,
    description: 'Heating, ventilation, and air conditioning',
    color: 'text-green-600',
    subCategories: [
      {
        id: 'repair',
        name: 'Repair',
        icon: Wrench,
        options: [
          { id: 'ductless-heating-ac', name: 'Ductless heating and AC services' },
          { id: 'repair-ac', name: 'Repair AC' },
          { id: 'repair-hvac', name: 'Repair HVAC' },
          { id: 'repair-ducts-vents', name: 'Repair ducts & vents' },
          { id: 'repair-heating-system', name: 'Repair heating system' },
          { id: 'repair-thermostat', name: 'Repair thermostat' },
        ],
      },
      {
        id: 'install',
        name: 'Install',
        icon: PlusSquare,
        options: [
          { id: 'install-ac', name: 'Install AC' },
          { id: 'install-ducts-vents', name: 'Install ducts & vents' },
          { id: 'install-heating-system', name: 'Install heating system' },
          { id: 'install-thermostat', name: 'Install thermostat' },
        ],
      },
      {
        id: 'maintenance',
        name: 'Maintenance',
        icon: RefreshCcw,
        options: [
          { id: 'ac-maintenance', name: 'AC maintenance' },
          { id: 'hvac-maintenance', name: 'HVAC maintenance' },
          { id: 'heating-maintenance', name: 'Heating maintenance' },
        ],
      },
      {
        id: 'other-hvac',
        name: 'Other',
        icon: Ellipsis,
        options: [],
      },
    ],
  },
  {
    id: 'electrical',
    name: 'Electrical',
    icon: Zap,
    description: 'Wiring, outlets, and electrical repairs',
    color: 'text-yellow-600',
    subCategories: [
      {
        id: 'repair',
        name: 'Repair',
        icon: Wrench,
        options: [
          { id: 'repair-fan', name: 'Repair fan' },
          { id: 'repair-light-fixtures', name: 'Repair light fixtures' },
          { id: 'repair-outlets-switches', name: 'Repair outlets or switches' },
          { id: 'repair-panel', name: 'Repair panel' },
          { id: 'restore-power', name: 'Restore power' },
        ],
      },
      {
        id: 'install',
        name: 'Install',
        icon: PlusSquare,
        options: [
          { id: 'install-electric-car-charger', name: 'Install electric car charger' },
          { id: 'install-fan', name: 'Install fan' },
          { id: 'install-ground-wire', name: 'Install ground wire' },
          { id: 'install-light-fixtures', name: 'Install light fixtures' },
          { id: 'install-outdoor-lighting', name: 'Install outdoor lighting' },
          { id: 'install-outlets-switches', name: 'Install outlets or switches' },
          { id: 'install-security-system', name: 'Install security system' },
          { id: 'relocate-outlets-switches', name: 'Relocate outlets or switches' },
          { id: 'remodeling', name: 'Remodeling' },
          { id: 'replace-upgrade-panel', name: 'Replace or upgrade panel' },
        ],
      },
      {
        id: 'other-electrical',
        name: 'Other',
        icon: Ellipsis,
        options: [],
      },
    ],
  },
];

// Helper functions
export const getCategoryById = (categoryId: string): ServiceCategory | undefined => {
  return services.find((category) => category.id === categoryId);
};

export const getSubCategoryById = (
  categoryId: string,
  subCategoryId: string,
): ServiceSubCategory | undefined => {
  const category = getCategoryById(categoryId);
  return category?.subCategories.find((sub) => sub.id === subCategoryId);
};

export const getOptionById = (
  categoryId: string,
  subCategoryId: string,
  optionId: string,
): ServiceOption | undefined => {
  const subCategory = getSubCategoryById(categoryId, subCategoryId);
  return subCategory?.options.find((option) => option.id === optionId);
};

// Form Field Types
export type FormFieldType =
  | 'short_text'
  | 'long_text'
  | 'email'
  | 'phone'
  | 'number'
  | 'dropdown'
  | 'multiple_choice'
  | 'checkboxes'
  | 'date'
  | 'time'
  | 'file_upload'
  | 'rating'
  | 'yes_no'
  | 'website'
  | 'legal'
  | 'payment'
  | 'address'
  | 'zip_code'
  | 'service_category'
  | 'service_type'
  | 'service_options'
  | 'appointment_slots'
  | 'calendar_view'
  | 'pricing_display';

export interface FormField {
  id: string;
  type: FormFieldType;
  label: string;
  placeholder?: string;
  help_text?: string;
  required: boolean;
  options?: string[];
  width?: 'full' | 'half' | 'third' | 'quarter';
  service_categories?: string[];
  service_types?: string[];
  min_value?: number;
  max_value?: number;
  pattern?: string;

  // Advanced validation
  validation?: {
    min_length?: number;
    max_length?: number;
    custom_regex?: string;
    custom_message?: string;
    file_types?: string[];
    max_file_size?: number; // in MB
    multiple_files?: boolean;
  };

  // Conditional logic
  conditional_logic?: {
    show_if?: {
      field_id: string;
      operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
      value: any;
    }[];
    required_if?: {
      field_id: string;
      operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
      value: any;
    }[];
  };

  // Styling and appearance
  styling?: {
    background_color?: string;
    text_color?: string;
    border_color?: string;
    border_radius?: number;
    font_size?: number;
    font_weight?: 'normal' | 'bold' | 'light';
    margin?: { top?: number; bottom?: number; left?: number; right?: number };
    padding?: { top?: number; bottom?: number; left?: number; right?: number };
    custom_css?: string;
  };

  // Field-specific configurations
  config?: {
    // For rating fields
    max_rating?: number;
    rating_style?: 'stars' | 'numbers' | 'emojis';

    // For date/time fields
    date_format?: string;
    time_format?: '12h' | '24h';
    min_date?: string;
    max_date?: string;

    // For dropdown/select fields
    allow_other?: boolean;
    searchable?: boolean;
    multiple_selection?: boolean;

    // For text fields
    auto_capitalize?: boolean;
    auto_complete?: string;

    // For number fields
    step?: number;
    currency?: string;

    // For file upload
    accept_types?: string[];
    max_files?: number;

    // For address fields
    auto_complete_address?: boolean;
    restrict_country?: string;

    // For service-specific fields
    show_pricing?: boolean;
    show_duration?: boolean;
    show_availability?: boolean;
  };
}

export interface FormPage {
  id: string;
  title: string;
  description?: string;
  fields: FormField[];
  button_text?: string;
  show_progress?: boolean;
  allow_back?: boolean;

  // Page-specific styling
  styling?: {
    background_color?: string;
    background_image?: string;
    text_color?: string;
    title_color?: string;
    description_color?: string;
    button_color?: string;
    button_text_color?: string;
    custom_css?: string;
  };

  // Page behavior
  behavior?: {
    auto_advance?: boolean;
    auto_advance_delay?: number; // seconds
    require_all_fields?: boolean;
    show_field_numbers?: boolean;
    animation_type?: 'fade' | 'slide' | 'none';
  };
}

export interface FormTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  preview_image?: string;
  tags: string[];
  usage_count: number;
  rating: number;
  pages: FormPage[];

  // Form settings
  settings: {
    navigation_type: 'vertical' | 'horizontal';
    show_progress_bar: boolean;
    success_message: string;
    enable_booking: boolean;
    show_pricing: boolean;
    dispatch_fee?: number;

    // Advanced settings
    allow_save_draft?: boolean;
    require_login?: boolean;
    max_submissions?: number;
    submission_limit_per_user?: number;
    start_date?: string;
    end_date?: string;
    redirect_url?: string;
    send_confirmation_email?: boolean;
    confirmation_email_template?: string;
    webhook_url?: string;

    // Form behavior
    show_field_numbers?: boolean;
    randomize_fields?: boolean;
    auto_save_progress?: boolean;
    show_time_to_complete?: boolean;
    estimated_time?: number; // minutes

    // Security
    enable_captcha?: boolean;
    password_protected?: boolean;
    access_password?: string;
    allowed_domains?: string[];

    // Analytics
    enable_analytics?: boolean;
    google_analytics_id?: string;
    facebook_pixel_id?: string;
  };

  // Global form theming
  theme: {
    // Colors
    primary_color?: string;
    secondary_color?: string;
    background_color?: string;
    text_color?: string;
    accent_color?: string;
    error_color?: string;
    success_color?: string;

    // Typography
    font_family?: string;
    font_size?: number;
    line_height?: number;

    // Layout
    container_width?: number;
    border_radius?: number;
    spacing?: number;

    // Branding
    logo_url?: string;
    logo_position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
    logo_size?: number;

    // Background
    background_image?: string;
    background_pattern?: string;
    background_overlay?: string;

    // Custom CSS
    custom_css?: string;
  };

  // Integrations
  integrations?: {
    // Email
    email_provider?: 'sendgrid' | 'mailchimp' | 'constant_contact' | 'custom';
    email_config?: Record<string, any>;

    // CRM
    crm_provider?: 'salesforce' | 'hubspot' | 'pipedrive' | 'custom';
    crm_config?: Record<string, any>;

    // Calendar
    calendar_provider?: 'google' | 'outlook' | 'calendly' | 'custom';
    calendar_config?: Record<string, any>;

    // Payment
    payment_provider?: 'stripe' | 'paypal' | 'square' | 'custom';
    payment_config?: Record<string, any>;

    // SMS
    sms_provider?: 'twilio' | 'textmagic' | 'custom';
    sms_config?: Record<string, any>;

    // Webhooks
    webhooks?: {
      url: string;
      events: string[];
      headers?: Record<string, string>;
    }[];
  };
}

// Import comprehensive templates
import { comprehensiveTemplates } from './comprehensive-templates';

export const formTemplates: FormTemplate[] = comprehensiveTemplates;
