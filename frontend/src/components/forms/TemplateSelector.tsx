'use client';

import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Droplet,
  Snowflake,
  Zap,
  FileText,
  Plus,
  Eye,
  ArrowRight,
  Leaf,
  Bug,
  Home,
} from 'lucide-react';
import { FormTemplate } from '@/types/forms';

interface TemplateSelectorProps {
  onTemplateSelect: (template: FormTemplate | null) => void;
  onStartFromScratch: () => void;
}

import { comprehensiveTemplates as formTemplates } from '@/data/comprehensive-templates';

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'roofing':
      return Home;
    case 'hvac':
      return Snowflake;
    case 'plumbing':
      return Droplet;
    case 'electrical':
      return Zap;
    case 'landscaping':
      return Leaf;
    case 'pest_control':
      return Bug;
    default:
      return FileText;
  }
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'roofing':
      return 'text-red-600 bg-red-50';
    case 'hvac':
      return 'text-blue-600 bg-blue-50';
    case 'plumbing':
      return 'text-indigo-600 bg-indigo-50';
    case 'electrical':
      return 'text-yellow-600 bg-yellow-50';
    case 'landscaping':
      return 'text-green-600 bg-green-50';
    case 'pest_control':
      return 'text-purple-600 bg-purple-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
};

export function TemplateSelector({ onTemplateSelect, onStartFromScratch }: TemplateSelectorProps) {
  return (
    <div
      className="min-h-screen"
      style={{ background: 'linear-gradient(to br, #ddd6fe, #ffffff, #dcfce7)' }}
    >
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-6"
            style={{ background: 'var(--gradient-primary)' }}
          >
            <span className="text-white text-2xl">📝</span>
          </div>
          <h1
            className="text-4xl font-bold mb-4"
            style={{ background: 'var(--gradient-primary)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}
          >
            Create New Service Request Form
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Choose from our pre-built templates or start from scratch to create your custom service
            request form
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          {/* Start from Scratch Option */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="mb-8"
          >
            <Card
              className="border-2 border-dashed border-white/30 hover:border-white/50 transition-colors backdrop-blur-sm shadow-2xl"
              style={{ background: 'rgba(255, 255, 255, 0.8)' }}
            >
              <CardContent className="p-8 text-center">
                <div
                  className="w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4"
                  style={{ background: 'var(--gradient-primary)' }}
                >
                  <Plus className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-2">Start from Scratch</h3>
                <p className="text-muted-foreground mb-6">
                  Build a completely custom form tailored to your specific needs
                </p>
                <Button
                  onClick={onStartFromScratch}
                  className="text-white"
                  style={{ background: 'var(--gradient-primary)' }}
                >
                  Create Custom Form
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Templates Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-foreground mb-2">Choose from Templates</h2>
            <p className="text-muted-foreground mb-6">
              Get started quickly with our professionally designed templates
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {formTemplates.map((template, index) => {
                const Icon = getCategoryIcon(template.category);
                const colorClass = getCategoryColor(template.category);

                return (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card
                      className="h-full hover:shadow-2xl transition-all duration-300 border border-white/20 cursor-pointer group backdrop-blur-sm"
                      style={{ background: 'rgba(255, 255, 255, 0.8)' }}
                    >
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <div
                            className="w-12 h-12 rounded-xl flex items-center justify-center"
                            style={{ backgroundColor: 'var(--color-primary)', opacity: 0.1 }}
                          >
                            <Icon className="w-6 h-6" style={{ color: 'var(--color-primary)' }} />
                          </div>
                          <div className="flex flex-col items-end gap-2">
                            <Badge
                              variant="secondary"
                              className="text-sm"
                              style={{ backgroundColor: 'var(--color-primary)', opacity: 0.1, color: 'var(--color-primary)' }}
                            >
                              {template.category
                                .replace('_', ' ')
                                .replace(/\b\w/g, (l) => l.toUpperCase())}
                            </Badge>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <span>⭐ {template.rating}</span>
                              <span>•</span>
                              <span>{template.usage_count} uses</span>
                            </div>
                          </div>
                        </div>
                        <CardTitle className="text-xl group-hover:text-primary transition-colors mt-4">
                          {template.name}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-muted-foreground text-sm mb-6 line-clamp-3">
                          {template.description}
                        </p>

                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 hover:bg-primary hover:text-primary-foreground"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(`/web-forms/${template.id}/preview`, '_blank');
                            }}
                          >
                            <Eye className="w-4 h-4 mr-2" />
                            Preview
                          </Button>
                          <Button
                            size="sm"
                            className="flex-1 text-white"
                            style={{ background: 'var(--gradient-primary)' }}
                            onClick={(e) => {
                              e.stopPropagation();
                              onTemplateSelect(template);
                            }}
                          >
                            Use Template
                            <ArrowRight className="w-4 h-4 ml-2" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Additional Info */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="text-center"
          >
            <Card className="bg-background border-0">
              <CardContent className="p-6">
                <h3 className="font-semibold text-foreground mb-2">Need help choosing?</h3>
                <p className="text-muted-foreground text-sm">
                  All templates can be fully customized after creation. You can always modify
                  fields, styling, and settings to match your needs.
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
