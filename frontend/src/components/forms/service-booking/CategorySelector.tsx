"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { services, type ServiceCategory } from "@/data/services";
import { ArrowLeft } from "lucide-react";

interface CategorySelectorProps {
  selectedCategory?: string;
  onCategorySelect: (categoryId: string) => void;
  onBack?: () => void;
  showBackButton?: boolean;
}

export function CategorySelector({ 
  selectedCategory, 
  onCategorySelect, 
  onBack,
  showBackButton = false 
}: CategorySelectorProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="max-w-4xl mx-auto"
    >
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-foreground mb-3">
          What service do you need?
        </h2>
        <p className="text-muted-foreground text-lg">
          Select the type of service you need assistance with
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {services.map((service) => {
          const Icon = service.icon;
          const isSelected = selectedCategory === service.id;
          
          return (
            <motion.div
              key={service.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <Card
                className={cn(
                  "cursor-pointer transition-all duration-300 hover:shadow-lg border-2 h-full",
                  isSelected
                    ? "border-primary bg-accent shadow-md"
                    : "border-border hover:border-primary/30"
                )}
                onClick={() => onCategorySelect(service.id)}
              >
                <CardContent className="p-8 text-center h-full flex flex-col justify-center">
                  <div className={cn(
                    "p-4 rounded-2xl bg-background mx-auto mb-4 w-fit shadow-sm",
                    service.color
                  )}>
                    <Icon className="w-8 h-8" />
                  </div>
                  <h3 className="font-bold text-xl text-foreground mb-2">
                    {service.name}
                  </h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {service.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {showBackButton && onBack && (
        <div className="flex justify-start">
          <Button
            variant="outline"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
        </div>
      )}
    </motion.div>
  );
}
