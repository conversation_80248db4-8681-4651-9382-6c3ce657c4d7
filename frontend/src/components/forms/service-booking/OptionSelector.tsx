"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { getCategoryById, getSubCategoryById } from "@/data/services";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { useState } from "react";

interface OptionSelectorProps {
  categoryId: string;
  subCategoryId: string;
  selectedOption?: string;
  description?: string;
  onOptionSelect: (optionId: string) => void;
  onDescriptionChange: (description: string) => void;
  onBack: () => void;
  onNext: () => void;
  canContinue: boolean;
}

export function OptionSelector({ 
  categoryId,
  subCategoryId,
  selectedOption, 
  description = "",
  onOptionSelect, 
  onDescriptionChange,
  onBack,
  onNext,
  canContinue
}: OptionSelectorProps) {
  const category = getCategoryById(categoryId);
  const subCategory = getSubCategoryById(categoryId, subCategoryId);
  
  if (!category || !subCategory) {
    return null;
  }

  const CategoryIcon = category.icon;
  const SubCategoryIcon = subCategory.icon;
  const hasOptions = subCategory.options.length > 0;

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className="max-w-4xl mx-auto"
    >
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className={cn("p-2 rounded-lg bg-background shadow-sm", category.color)}>
            <CategoryIcon className="w-5 h-5" />
          </div>
          <div className="p-2 rounded-lg bg-background shadow-sm">
            <SubCategoryIcon className="w-5 h-5 text-muted-foreground" />
          </div>
          <h2 className="text-2xl font-bold text-foreground">
            {category.name} - {subCategory.name}
          </h2>
        </div>
        <p className="text-muted-foreground text-lg">
          {hasOptions 
            ? `What specific ${subCategory.name.toLowerCase()} service do you need?`
            : `Tell us about your ${subCategory.name.toLowerCase()} needs`
          }
        </p>
      </div>

      {hasOptions && (
        <div className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {subCategory.options.map((option) => {
              const isSelected = selectedOption === option.id;
              
              return (
                <motion.div
                  key={option.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <Button
                    variant={isSelected ? "default" : "outline"}
                    onClick={() => onOptionSelect(option.id)}
                    className={cn(
                      "w-full h-auto p-4 text-left justify-start transition-all duration-200",
                      isSelected 
                        ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md" 
                        : "hover:border-primary/50"
                    )}
                  >
                    <div>
                      <div className="font-medium text-sm">
                        {option.name}
                      </div>
                      {option.description && (
                        <div className="text-xs opacity-80 mt-1">
                          {option.description}
                        </div>
                      )}
                    </div>
                  </Button>
                </motion.div>
              );
            })}
          </div>
        </div>
      )}

      <div className="mb-8">
        <label className="block text-sm font-medium text-foreground mb-3">
          {hasOptions 
            ? "Additional details (optional)"
            : "Describe your service needs"
          }
        </label>
        <Textarea
          value={description}
          onChange={(e) => onDescriptionChange(e.target.value)}
          placeholder={hasOptions 
            ? "Tell us more about what you need help with..."
            : "Please describe your service requirements in detail..."
          }
          className="min-h-24 resize-none"
          rows={4}
        />
      </div>

      <div className="flex gap-4">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <Button
          onClick={onNext}
          disabled={!canContinue}
          className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex items-center justify-center gap-2"
        >
          Continue
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </motion.div>
  );
}
