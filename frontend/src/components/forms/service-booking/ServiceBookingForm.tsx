'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Progress } from '@/components/ui/progress';
import { CategorySelector } from './CategorySelector';
import { SubCategorySelector } from './SubCategorySelector';
import { OptionSelector } from './OptionSelector';
import { AddressStep } from './AddressStep';
import { ConfirmationScreen } from './ConfirmationScreen';
import { getCategoryById, getSubCategoryById } from '@/data/services';

interface BookingData {
  categoryId?: string;
  subCategoryId?: string;
  optionId?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  locationNotes?: string;
  date?: Date;
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  notes?: string;
}

const STEPS = [
  { id: 1, name: 'Service Type', description: 'Choose your service category' },
  { id: 2, name: 'Service Details', description: 'Select specific service' },
  { id: 3, name: 'Requirements', description: 'Describe your needs' },
  { id: 4, name: 'Location', description: 'Where you need service' },
  { id: 5, name: 'Confirmation', description: 'Review and confirm' },
];

interface ServiceBookingFormProps {
  onComplete?: (data: BookingData) => void;
  initialData?: Partial<BookingData>;
}

export function ServiceBookingForm({ onComplete, initialData = {} }: ServiceBookingFormProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [bookingData, setBookingData] = useState<BookingData>(initialData);

  const updateBookingData = (updates: Partial<BookingData>) => {
    setBookingData((prev) => ({ ...prev, ...updates }));
  };

  const goToNextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const handleCategorySelect = (categoryId: string) => {
    updateBookingData({
      categoryId,
      subCategoryId: undefined,
      optionId: undefined,
    });
    goToNextStep();
  };

  const handleSubCategorySelect = (subCategoryId: string) => {
    updateBookingData({ subCategoryId, optionId: undefined });

    // If subcategory has no options (like "Other"), skip to address step
    const category = getCategoryById(bookingData.categoryId!);
    const subCategory = getSubCategoryById(bookingData.categoryId!, subCategoryId);

    if (subCategory && subCategory.options.length === 0) {
      setCurrentStep(4); // Skip to address step
    } else {
      goToNextStep();
    }
  };

  const handleOptionSelect = (optionId: string) => {
    updateBookingData({ optionId });
  };

  const handleDescriptionChange = (description: string) => {
    updateBookingData({ description });
  };

  const handleAddressNext = (addressData: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
    notes?: string;
  }) => {
    updateBookingData({
      address: addressData.address,
      city: addressData.city,
      state: addressData.state,
      zipCode: addressData.zipCode,
      locationNotes: addressData.notes,
    });
    goToNextStep();
  };

  const handleConfirm = () => {
    if (onComplete) {
      onComplete(bookingData);
    }
  };

  const canContinueFromStep2 = () => {
    return bookingData.subCategoryId !== undefined;
  };

  const canContinueFromStep3 = () => {
    const category = getCategoryById(bookingData.categoryId!);
    const subCategory = getSubCategoryById(bookingData.categoryId!, bookingData.subCategoryId!);

    if (!subCategory) return false;

    // If subcategory has options, require option selection
    if (subCategory.options.length > 0) {
      return bookingData.optionId !== undefined;
    }

    // If no options, require description
    return bookingData.description && bookingData.description.trim().length > 0;
  };

  const progressPercentage = (currentStep / STEPS.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            Service Request Form
          </h1>
          <p className="text-muted-foreground text-lg">
            Tell us what you need and we'll connect you with the right professional
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex items-center justify-between mb-4">
            {STEPS.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                  ${
                    currentStep >= step.id
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }
                `}
                >
                  {step.id}
                </div>
                {index < STEPS.length - 1 && (
                  <div
                    className={`
                    w-16 h-1 mx-2
                    ${currentStep > step.id ? 'bg-gradient-to-r from-blue-600 to-purple-600' : 'bg-gray-200'}
                  `}
                  />
                )}
              </div>
            ))}
          </div>
          <Progress value={progressPercentage} className="h-2" />
          <div className="flex justify-between mt-2">
            {STEPS.map((step) => (
              <div key={step.id} className="text-center">
                <div className="text-sm font-medium text-foreground">{step.name}</div>
                <div className="text-xs text-muted-foreground">{step.description}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Form Steps */}
        <div className="max-w-6xl mx-auto bg-white rounded-2xl shadow-xl p-8">
          <AnimatePresence mode="wait">
            {currentStep === 1 && (
              <CategorySelector
                key="category"
                selectedCategory={bookingData.categoryId}
                onCategorySelect={handleCategorySelect}
              />
            )}

            {currentStep === 2 && bookingData.categoryId && (
              <SubCategorySelector
                key="subcategory"
                categoryId={bookingData.categoryId}
                selectedSubCategory={bookingData.subCategoryId}
                onSubCategorySelect={handleSubCategorySelect}
                onBack={goToPreviousStep}
                onNext={goToNextStep}
                canContinue={canContinueFromStep2()}
              />
            )}

            {currentStep === 3 && bookingData.categoryId && bookingData.subCategoryId && (
              <OptionSelector
                key="options"
                categoryId={bookingData.categoryId}
                subCategoryId={bookingData.subCategoryId}
                selectedOption={bookingData.optionId}
                description={bookingData.description}
                onOptionSelect={handleOptionSelect}
                onDescriptionChange={handleDescriptionChange}
                onBack={goToPreviousStep}
                onNext={goToNextStep}
                canContinue={canContinueFromStep3()}
              />
            )}

            {currentStep === 4 && bookingData.categoryId && bookingData.subCategoryId && (
              <AddressStep
                key="address"
                initialData={{
                  address: bookingData.address,
                  city: bookingData.city,
                  state: bookingData.state,
                  zipCode: bookingData.zipCode,
                  notes: bookingData.locationNotes,
                }}
                onNext={handleAddressNext}
                onBack={goToPreviousStep}
              />
            )}

            {currentStep === 5 && bookingData.categoryId && bookingData.subCategoryId && (
              <ConfirmationScreen
                key="confirmation"
                data={bookingData as any}
                onBack={goToPreviousStep}
                onConfirm={handleConfirm}
              />
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
