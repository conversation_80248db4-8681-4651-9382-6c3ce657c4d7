'use client';

import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getCategoryById, getSubCategoryById, getOptionById } from '@/data/services';
import { ArrowLeft, CheckCircle, Calendar, MapPin, User, Phone, Mail } from 'lucide-react';

interface BookingData {
  categoryId: string;
  subCategoryId: string;
  optionId?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  locationNotes?: string;
  date?: Date;
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  notes?: string;
}

interface ConfirmationScreenProps {
  data: BookingData;
  onBack: () => void;
  onConfirm: () => void;
}

export function ConfirmationScreen({ data, onBack, onConfirm }: ConfirmationScreenProps) {
  const category = getCategoryById(data.categoryId);
  const subCategory = getSubCategoryById(data.categoryId, data.subCategoryId);
  const option = data.optionId
    ? getOptionById(data.categoryId, data.subCategoryId, data.optionId)
    : null;

  if (!category || !subCategory) {
    return null;
  }

  const CategoryIcon = category.icon;
  const SubCategoryIcon = subCategory.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="max-w-3xl mx-auto"
    >
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h2 className="text-3xl font-bold text-foreground mb-2">Review Your Service Request</h2>
        <p className="text-muted-foreground text-lg">
          Please confirm the details below before submitting your request
        </p>
      </div>

      <div className="space-y-6">
        {/* Service Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-background shadow-sm">
                <CategoryIcon className="w-5 h-5 text-blue-600" />
              </div>
              Service Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="flex items-center gap-2">
                <CategoryIcon className="w-3 h-3" />
                {category.name}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-2">
                <SubCategoryIcon className="w-3 h-3" />
                {subCategory.name}
              </Badge>
            </div>

            {option && (
              <div>
                <h4 className="font-medium text-foreground mb-1">Specific Service:</h4>
                <p className="text-muted-foreground">{option.name}</p>
              </div>
            )}

            {data.description && (
              <div>
                <h4 className="font-medium text-foreground mb-1">Description:</h4>
                <p className="text-muted-foreground">{data.description}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contact Information */}
        {(data.firstName || data.lastName || data.email || data.phone) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-background shadow-sm">
                  <User className="w-5 h-5 text-green-600" />
                </div>
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {(data.firstName || data.lastName) && (
                <div className="flex items-center gap-3">
                  <User className="w-4 h-4 text-muted-foreground" />
                  <span>{[data.firstName, data.lastName].filter(Boolean).join(' ')}</span>
                </div>
              )}
              {data.email && (
                <div className="flex items-center gap-3">
                  <Mail className="w-4 h-4 text-muted-foreground" />
                  <span>{data.email}</span>
                </div>
              )}
              {data.phone && (
                <div className="flex items-center gap-3">
                  <Phone className="w-4 h-4 text-muted-foreground" />
                  <span>{data.phone}</span>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Service Location */}
        {(data.address || data.city || data.state || data.zipCode) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-background shadow-sm">
                  <MapPin className="w-5 h-5 text-purple-600" />
                </div>
                Service Location
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {data.address && (
                <div className="flex items-start gap-3">
                  <MapPin className="w-4 h-4 text-muted-foreground mt-0.5" />
                  <div>
                    <div className="font-medium">{data.address}</div>
                    <div className="text-muted-foreground text-sm">
                      {[data.city, data.state, data.zipCode].filter(Boolean).join(', ')}
                    </div>
                  </div>
                </div>
              )}
              {data.locationNotes && (
                <div className="flex items-start gap-3">
                  <div className="w-4 h-4" /> {/* Spacer */}
                  <div className="text-sm text-muted-foreground">
                    <strong>Notes:</strong> {data.locationNotes}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Schedule */}
        {(data.date || data.time) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-background shadow-sm">
                  <Calendar className="w-5 h-5 text-green-600" />
                </div>
                Schedule
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <span>
                  {data.date && data.date.toLocaleDateString()}
                  {data.date && data.time && ' at '}
                  {data.time}
                </span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Additional Notes */}
        {data.notes && (
          <Card>
            <CardHeader>
              <CardTitle>Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">{data.notes}</p>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-4 mt-8">
        <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <Button
          onClick={onConfirm}
          className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white"
        >
          Confirm Service Request
        </Button>
      </div>
    </motion.div>
  );
}
