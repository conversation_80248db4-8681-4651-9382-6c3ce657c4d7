"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { getCategoryById, type ServiceSubCategory } from "@/data/services";
import { ArrowLeft, ArrowRight } from "lucide-react";

interface SubCategorySelectorProps {
  categoryId: string;
  selectedSubCategory?: string;
  onSubCategorySelect: (subCategoryId: string) => void;
  onBack: () => void;
  onNext: () => void;
  canContinue: boolean;
}

export function SubCategorySelector({ 
  categoryId,
  selectedSubCategory, 
  onSubCategorySelect, 
  onBack,
  onNext,
  canContinue
}: SubCategorySelectorProps) {
  const category = getCategoryById(categoryId);
  
  if (!category) {
    return null;
  }

  const CategoryIcon = category.icon;

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className="max-w-4xl mx-auto"
    >
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className={cn("p-2 rounded-lg bg-background shadow-sm", category.color)}>
            <CategoryIcon className="w-6 h-6" />
          </div>
          <h2 className="text-3xl font-bold text-foreground">
            {category.name} Services
          </h2>
        </div>
        <p className="text-muted-foreground text-lg">
          What type of {category.name.toLowerCase()} service do you need?
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {category.subCategories.map((subCategory) => {
          const Icon = subCategory.icon;
          const isSelected = selectedSubCategory === subCategory.id;
          
          return (
            <motion.div
              key={subCategory.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <Card
                className={cn(
                  "cursor-pointer transition-all duration-300 hover:shadow-md border-2",
                  isSelected
                    ? "border-primary bg-accent shadow-sm"
                    : "border-border hover:border-primary/30"
                )}
                onClick={() => onSubCategorySelect(subCategory.id)}
              >
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-lg bg-background mx-auto mb-3 w-fit shadow-sm">
                    <Icon className="w-5 h-5 text-muted-foreground" />
                  </div>
                  <h3 className="font-semibold text-foreground mb-1">
                    {subCategory.name}
                  </h3>
                  <p className="text-xs text-muted-foreground">
                    {subCategory.options.length > 0 
                      ? `${subCategory.options.length} options available`
                      : "Custom service"
                    }
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      <div className="flex gap-4">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <Button
          onClick={onNext}
          disabled={!canContinue}
          className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex items-center justify-center gap-2"
        >
          Continue
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </motion.div>
  );
}
