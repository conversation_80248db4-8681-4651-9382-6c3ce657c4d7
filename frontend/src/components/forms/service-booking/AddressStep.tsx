'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AddressAutocomplete } from '../AddressAutocomplete';
import { ArrowLeft, ArrowRight, MapPin, Home } from 'lucide-react';

interface AddressStepProps {
  initialData?: {
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    notes?: string;
  };
  onNext: (data: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
    notes?: string;
  }) => void;
  onBack: () => void;
}

export function AddressStep({ initialData = {}, onNext, onBack }: AddressStepProps) {
  const [address, setAddress] = useState(initialData.address || '');
  const [city, setCity] = useState(initialData.city || '');
  const [state, setState] = useState(initialData.state || '');
  const [zipCode, setZipCode] = useState(initialData.zipCode || '');
  const [notes, setNotes] = useState(initialData.notes || '');

  const handleAddressSelect = (fullAddress: string, placeId: string) => {
    // Parse the address components (in a real implementation, you'd use Google Places Details API)
    const parts = fullAddress.split(', ');
    if (parts.length >= 3) {
      setAddress(parts[0]);
      setCity(parts[1]);
      const stateZip = parts[2].split(' ');
      if (stateZip.length >= 2) {
        setState(stateZip[0]);
        setZipCode(stateZip[1]);
      }
    }
  };

  const handleNext = () => {
    if (!address.trim() || !city.trim() || !state.trim() || !zipCode.trim()) {
      return;
    }

    onNext({
      address: address.trim(),
      city: city.trim(),
      state: state.trim(),
      zipCode: zipCode.trim(),
      notes: notes.trim(),
    });
  };

  const canContinue = address.trim() && city.trim() && state.trim() && zipCode.trim();

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className="max-w-2xl mx-auto"
    >
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Home className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-3xl font-bold text-foreground mb-2">Service Location</h2>
        <p className="text-muted-foreground text-lg">Where do you need the service performed?</p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Address Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label htmlFor="address" className="text-sm font-medium">
              Street Address *
            </Label>
            <AddressAutocomplete
              value={address}
              onChange={setAddress}
              onAddressSelect={handleAddressSelect}
              placeholder="Start typing your address..."
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="city" className="text-sm font-medium">
                City *
              </Label>
              <Input
                id="city"
                type="text"
                value={city}
                onChange={(e) => setCity(e.target.value)}
                placeholder="Enter city"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="state" className="text-sm font-medium">
                State *
              </Label>
              <Input
                id="state"
                type="text"
                value={state}
                onChange={(e) => setState(e.target.value)}
                placeholder="Enter state"
                className="mt-1"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="zipCode" className="text-sm font-medium">
                ZIP Code *
              </Label>
              <Input
                id="zipCode"
                type="text"
                value={zipCode}
                onChange={(e) => setZipCode(e.target.value)}
                placeholder="Enter ZIP code"
                className="mt-1"
              />
            </div>
            <div className="flex items-end">
              <div className="text-xs text-muted-foreground">* Required fields</div>
            </div>
          </div>

          <div>
            <Label htmlFor="notes" className="text-sm font-medium">
              Additional Location Notes (Optional)
            </Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Any special instructions for finding your location? (e.g., apartment number, gate code, parking instructions)"
              className="mt-1 min-h-20"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-4">
        <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!canContinue}
          className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex items-center justify-center gap-2"
        >
          Continue
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </motion.div>
  );
}
