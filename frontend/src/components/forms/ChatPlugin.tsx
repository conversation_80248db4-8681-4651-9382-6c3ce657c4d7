'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Send,
  MessageCircle,
  RotateCcw,
  CheckCircle,
  Wrench,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface ChatMessage {
  id: string;
  type: 'bot' | 'user';
  content: string | React.ReactNode;
  timestamp: Date;
  options?: string[];
  inputType?: 'text' | 'email' | 'tel' | 'date' | 'time';
  placeholder?: string;
}

interface ChatPluginProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
  className?: string;
  companyName?: string;
  companyLogo?: string;
}

interface FloatingChatButtonProps {
  onBookingComplete?: (data: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

// Chat Message Component
function ChatMessageComponent({
  message,
  onOptionSelect,
}: {
  message: ChatMessage;
  onOptionSelect: (option: string) => void;
}) {
  if (message.type === 'user') {
    return (
      <motion.div 
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        className="flex justify-end"
      >
        <div 
          className="max-w-[80%] rounded-lg px-3 py-2 text-sm text-white"
          style={{ background: 'var(--gradient-primary)' }}
        >
          {message.content}
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="flex items-start gap-2"
    >
      <div 
        className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
        style={{ backgroundColor: 'var(--color-primary)', opacity: 0.1 }}
      >
        <MessageCircle className="w-3 h-3" style={{ color: 'var(--color-primary)' }} />
      </div>
      <div className="flex-1">
        {typeof message.content === 'string' ? (
          message.content && (
            <div className="bg-muted rounded-lg px-3 py-2 text-sm text-foreground mb-2">
              {message.content}
            </div>
          )
        ) : (
          message.content
        )}

        {/* Option buttons */}
        {message.options && (
          <div className="flex flex-wrap gap-2">
            {message.options.map((option, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => onOptionSelect(option)}
                className="text-xs hover:bg-primary hover:text-primary-foreground transition-colors"
              >
                {option}
              </Button>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
}

export function ChatPlugin({
  isOpen,
  onClose,
  onComplete,
  className,
  companyName = 'Home Services',
  companyLogo,
}: ChatPluginProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>({});

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messageIdCounter = useRef(0);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (inputRef.current && messages.length > 0) {
      inputRef.current.focus();
    }
  }, [messages]);

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      startConversation();
    }
  }, [isOpen]);

  const startConversation = () => {
    setMessages([]);
    setFormData({});
    setCurrentStep(0);
    messageIdCounter.current = 0;

    const welcomeMessage: ChatMessage = {
      id: `${Date.now()}-${messageIdCounter.current++}`,
      type: 'bot',
      content: `Hi! Welcome to ${companyName}. I'm here to help you schedule a service appointment. Let's get started!`,
      timestamp: new Date(),
      options: ['Get Started', 'Learn More'],
    };

    setMessages([welcomeMessage]);
  };

  const addBotMessage = (
    content: string | React.ReactNode,
    options?: string[],
    inputType?: ChatMessage['inputType'],
    placeholder?: string
  ) => {
    setIsTyping(true);

    setTimeout(() => {
      const message: ChatMessage = {
        id: `${Date.now()}-${messageIdCounter.current++}`,
        type: 'bot',
        content,
        timestamp: new Date(),
        options,
        inputType,
        placeholder,
      };

      setMessages((prev) => [...prev, message]);
      setIsTyping(false);
    }, 1000);
  };

  const addUserMessage = (content: string) => {
    const message: ChatMessage = {
      id: `${Date.now()}-${messageIdCounter.current++}`,
      type: 'user',
      content,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, message]);
    setInputValue('');
  };

  const handleOptionSelect = (option: string) => {
    addUserMessage(option);
    processUserResponse(option);
  };

  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    addUserMessage(inputValue);
    processUserResponse(inputValue);
  };

  const processUserResponse = (response: string) => {
    const updatedData = { ...formData };

    // Simple conversation flow
    switch (currentStep) {
      case 0: // Welcome
        addBotMessage('Great! What\'s your zip code?', undefined, 'text', 'Enter your zip code');
        setCurrentStep(1);
        break;
      case 1: // Zip code
        updatedData.zipCode = response;
        addBotMessage(`Perfect! Your zip code is ${response}. What\'s your name?`, undefined, 'text', 'Enter your full name');
        setCurrentStep(2);
        break;
      case 2: // Name
        updatedData.name = response;
        addBotMessage(`Nice to meet you, ${response}! What\'s your email address?`, undefined, 'email', 'Enter your email');
        setCurrentStep(3);
        break;
      case 3: // Email
        updatedData.email = response;
        addBotMessage(`Got it! What\'s your phone number?`, undefined, 'tel', 'Enter your phone number');
        setCurrentStep(4);
        break;
      case 4: // Phone
        updatedData.phone = response;
        addBotMessage('What service do you need?', [
          'Plumbing',
          'HVAC',
          'Electrical',
          'Other'
        ]);
        setCurrentStep(5);
        break;
      case 5: // Service
        updatedData.service = response;
        addBotMessage(`Perfect! You need ${response} service. Can you describe the issue?`, undefined, 'text', 'Describe your issue...');
        setCurrentStep(6);
        break;
      case 6: // Description
        updatedData.description = response;
        addBotMessage('When would you like to schedule the service?', [
          'Today',
          'Tomorrow',
          'This Week',
          'Next Week'
        ]);
        setCurrentStep(7);
        break;
      case 7: // Schedule
        updatedData.schedule = response;
        // Show confirmation
        const confirmationContent = (
          <div className="space-y-2">
            <p>Here's your booking summary:</p>
            <div className="bg-white p-3 rounded border text-sm">
              <p><strong>Name:</strong> {updatedData.name}</p>
              <p><strong>Email:</strong> {updatedData.email}</p>
              <p><strong>Phone:</strong> {updatedData.phone}</p>
              <p><strong>Service:</strong> {updatedData.service}</p>
              <p><strong>Schedule:</strong> {updatedData.schedule}</p>
            </div>
          </div>
        );
        addBotMessage(confirmationContent, ['Confirm Booking', 'Make Changes']);
        setCurrentStep(8);
        break;
      case 8: // Confirmation
        if (response.includes('Confirm')) {
          addBotMessage('🎉 Booking confirmed! We\'ll contact you shortly to finalize the details.', ['Book Another', 'Close']);
          setCurrentStep(9);
          onComplete?.(updatedData);
        } else {
          startConversation();
          return;
        }
        break;
      case 9: // Complete
        if (response.includes('Book Another')) {
          startConversation();
          return;
        } else {
          onClose();
          return;
        }
    }

    setFormData(updatedData);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className={cn(
            'fixed bottom-4 right-4 z-50',
            className
          )}
        >
          <Card className="w-full max-w-md h-[600px] flex flex-col shadow-2xl">
            {/* Header */}
            <div 
              className="flex items-center justify-between p-4 text-white rounded-t-lg"
              style={{ background: 'var(--gradient-primary)' }}
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <Wrench className="w-4 h-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-sm">{companyName}</h3>
                  <p className="text-xs opacity-90">Booking Assistant</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={startConversation}
                  className="text-white hover:bg-white/20 h-8 w-8"
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="text-white hover:bg-white/20 h-8 w-8"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <ChatMessageComponent
                  key={message.id}
                  message={message}
                  onOptionSelect={handleOptionSelect}
                />
              ))}

              {/* Typing indicator */}
              {isTyping && (
                <motion.div 
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex items-center gap-2 text-muted-foreground"
                >
                  <div 
                    className="w-6 h-6 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: 'var(--color-primary)', opacity: 0.1 }}
                  >
                    <MessageCircle className="w-3 h-3" style={{ color: 'var(--color-primary)' }} />
                  </div>
                  <div className="flex gap-1">
                    <div className="w-2 h-2 rounded-full animate-bounce" style={{ backgroundColor: 'var(--color-primary)', opacity: 0.6 }} />
                    <div 
                      className="w-2 h-2 rounded-full animate-bounce" 
                      style={{ 
                        backgroundColor: 'var(--color-primary)', 
                        opacity: 0.6,
                        animationDelay: '0.1s' 
                      }} 
                    />
                    <div 
                      className="w-2 h-2 rounded-full animate-bounce" 
                      style={{ 
                        backgroundColor: 'var(--color-primary)', 
                        opacity: 0.6,
                        animationDelay: '0.2s' 
                      }} 
                    />
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <form onSubmit={handleInputSubmit} className="p-4 border-t">
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1"
                />
                <Button
                  type="submit"
                  size="icon"
                  disabled={!inputValue.trim()}
                  style={{ background: 'var(--gradient-primary)' }}
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </form>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Floating Chat Button Component
export function FloatingChatButton({
  onBookingComplete,
  onBookingStart,
  className,
}: FloatingChatButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <motion.div 
        className={cn('fixed bottom-6 right-6 z-40', className)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-14 h-14 rounded-full shadow-lg hover:shadow-xl p-0"
          style={{ background: 'var(--gradient-primary)' }}
        >
          <MessageCircle className="w-6 h-6" />
        </Button>
      </motion.div>

      <ChatPlugin
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
          setIsOpen(false);
        }}
      />
    </>
  );
}
