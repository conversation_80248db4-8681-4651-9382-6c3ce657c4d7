'use client';

import React, { useState, useImperative<PERSON><PERSON>le, Ref } from 'react';
import { FormTemplate, FormField, FormFieldType } from '@/types/forms';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { ColorPicker } from '@/components/ui/color-picker';
import { FormRenderer } from './FormRenderer';
// import { FieldEditor } from './fields/FormField';
import {
  Plus,
  Trash2,
  FileText,
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  List,
  CheckSquare,
  Type,
  Hash,
  Clock,
  User,
  Building,
  Wrench,
  Home,
  Star,
  Globe,
  Scale,
  CreditCard,
} from 'lucide-react';

export interface FormBuilderProps {
  template?: FormTemplate;
  showPreview?: boolean;
}

export interface FormBuilderRef {
  getCurrentFormTemplate: () => FormTemplate;
}

// Field type icons mapping
const fieldTypeIcons: Record<FormFieldType, React.ComponentType<any>> = {
  short_text: Type,
  long_text: FileText,
  email: Mail,
  phone: Phone,
  number: Hash,
  dropdown: List,
  multiple_choice: CheckSquare,
  checkboxes: CheckSquare,
  date: Calendar,
  time: Clock,
  file_upload: Plus,
  rating: Star,
  yes_no: CheckSquare,
  website: Globe,
  legal: Scale,
  payment: CreditCard,
  address: Home,
  zip_code: MapPin,
  service_category: Building,
  service_type: Wrench,
  service_options: List,
  appointment_slots: Clock,
  calendar_view: Calendar,
  pricing_display: DollarSign,
};

// Field type labels
const fieldTypeLabels: Record<FormFieldType, string> = {
  short_text: 'Short Text',
  long_text: 'Long Text',
  email: 'Email',
  phone: 'Phone Number',
  number: 'Number',
  dropdown: 'Dropdown',
  multiple_choice: 'Multiple Choice',
  checkboxes: 'Checkboxes',
  date: 'Date Picker',
  time: 'Time Picker',
  file_upload: 'File Upload',
  rating: 'Rating',
  yes_no: 'Yes/No',
  website: 'Website',
  legal: 'Legal',
  payment: 'Payment',
  address: 'Address',
  zip_code: 'Zip Code',
  service_category: 'Service Category',
  service_type: 'Service Type',
  service_options: 'Service Options',
  appointment_slots: 'Appointment Slots',
  calendar_view: 'Calendar View',
  pricing_display: 'Pricing Display',
};

export const FormBuilder = React.forwardRef<FormBuilderRef, FormBuilderProps>(
  ({ template, showPreview }, ref) => {
    const [formTemplate, setFormTemplate] = useState<FormTemplate>(
      template || {
        id: 'new-form',
        name: 'New Form',
        description: 'A new form created with the form builder',
        category: 'custom',
        tags: [],
        usage_count: 0,
        rating: 0,
        pages: [
          {
            id: 'page-1',
            title: 'Welcome to our form',
            description: 'Please fill out the information below',
            fields: [],
          },
        ],
        settings: {
          navigation_type: 'vertical',
          show_progress_bar: true,
          success_message: 'Thank you for your submission!',
          enable_booking: false,
          show_pricing: false,
        },
        theme: {
          primary_color: '#3b82f6',
          secondary_color: '#64748b',
          background_color: '#ffffff',
          text_color: '#1f2937',
          accent_color: '#10b981',
          font_family: 'Inter, sans-serif',
          font_size: 16,
          border_radius: 8,
          spacing: 16,
        },
        integrations: {},
      },
    );

    useImperativeHandle(ref, () => ({
      getCurrentFormTemplate: () => formTemplate,
    }));

    const [selectedPageIndex, setSelectedPageIndex] = useState(0);
    const [selectedFieldIndex, setSelectedFieldIndex] = useState<number | null>(null);
    const [activeTab, setActiveTab] = useState('builder');

    const currentPage = formTemplate.pages[selectedPageIndex];

    // Helper functions
    const addField = (type: FormFieldType) => {
      const newField: FormField = {
        id: `field-${Date.now()}`,
        type,
        label: `New ${fieldTypeLabels[type]}`,
        placeholder: `Enter ${fieldTypeLabels[type].toLowerCase()}`,
        required: false,
        validation: {},
      };

      const updatedPages = [...formTemplate.pages];
      updatedPages[selectedPageIndex] = {
        ...currentPage,
        fields: [...currentPage.fields, newField],
      };
      setFormTemplate(prev => ({ ...prev, pages: updatedPages }));
      setSelectedFieldIndex(currentPage.fields.length);
    };

    const removeField = (index: number) => {
      const updatedPages = [...formTemplate.pages];
      updatedPages[selectedPageIndex] = {
        ...currentPage,
        fields: currentPage.fields.filter((_, i) => i !== index),
      };
      setFormTemplate(prev => ({ ...prev, pages: updatedPages }));
      setSelectedFieldIndex(null);
    };

    const updateField = (index: number, updates: Partial<FormField>) => {
      const updatedPages = [...formTemplate.pages];
      updatedPages[selectedPageIndex] = {
        ...currentPage,
        fields: currentPage.fields.map((field, i) =>
          i === index ? { ...field, ...updates } : field
        ),
      };
      setFormTemplate(prev => ({ ...prev, pages: updatedPages }));
    };

    const addPage = () => {
      const newPage = {
        id: `page-${Date.now()}`,
        title: `Page ${formTemplate.pages.length + 1}`,
        description: 'New page description',
        fields: [],
      };
      setFormTemplate(prev => ({
        ...prev,
        pages: [...prev.pages, newPage],
      }));
      setSelectedPageIndex(formTemplate.pages.length);
    };

    if (showPreview) {
      return (
        <FormRenderer
          template={formTemplate}
          onSubmit={(data) => {
            console.log('Form submitted:', data);
          }}
          customProps={{
            zip_code: {
              headerTitle: 'Your Local Service Pro',
            },
          }}
        />
      );
    }

    return (
      <div className="min-h-screen">
        <div className="flex h-screen">
          {/* Left Panel - Field Palette */}
          <div className="w-80 bg-card border-r border-border flex flex-col">
            <div className="p-6 border-b border-border">
              <h2 className="text-lg font-semibold text-foreground">Add Fields</h2>
              <p className="text-sm text-muted-foreground mt-1">Click to add fields to your form</p>
            </div>
            <div className="flex-1 overflow-y-auto p-4 space-y-2">
              {Object.entries(fieldTypeLabels).map(([type, label]) => {
                const Icon = fieldTypeIcons[type as FormFieldType];
                return (
                  <Button
                    key={type}
                    variant="ghost"
                    size="sm"
                    onClick={() => addField(type as FormFieldType)}
                    className="w-full justify-start h-12 text-left hover:bg-accent/50 transition-colors"
                  >
                    <Icon className="w-5 h-5 mr-3 text-primary" />
                    <span className="font-medium">{label}</span>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Center Panel - Form Canvas */}
          <div className="flex-1 flex flex-col">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <div className="border-b bg-card px-6 py-4 flex items-center justify-between">
                <TabsList className="grid w-fit grid-cols-4">
                  <TabsTrigger value="builder">Builder</TabsTrigger>
                  <TabsTrigger value="theme">Theme</TabsTrigger>
                  <TabsTrigger value="settings">Settings</TabsTrigger>
                  <TabsTrigger value="integrations">Integrations</TabsTrigger>
                </TabsList>
                <div className="flex gap-2">
                  {formTemplate.pages.map((page, index) => (
                    <Button
                      key={page.id}
                      variant={selectedPageIndex === index ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedPageIndex(index)}
                      className="h-8"
                    >
                      Page {index + 1}
                    </Button>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={addPage}
                    className="h-8"
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <TabsContent value="builder" className="flex-1 overflow-y-auto p-6">
                <div className="max-w-2xl mx-auto space-y-6">
                  {/* Page Header */}
                  <div className="text-center space-y-2">
                    <h1 className="text-2xl font-bold text-foreground">{currentPage.title}</h1>
                    {currentPage.description && (
                      <p className="text-muted-foreground">{currentPage.description}</p>
                    )}
                  </div>

                  {/* Form Fields */}
                  <div className="space-y-4">
                    {currentPage.fields.length === 0 ? (
                      <div className="text-center py-12 border-2 border-dashed border-border rounded-lg">
                        <div className="text-muted-foreground">
                          <Plus className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <p className="text-lg font-medium">No fields yet</p>
                          <p className="text-sm">Add fields from the left panel to get started</p>
                        </div>
                      </div>
                    ) : (
                      currentPage.fields.map((field, index) => {
                        const Icon = fieldTypeIcons[field.type];
                        return (
                          <div
                            key={field.id}
                            className={`group relative p-6 border-2 rounded-xl transition-all cursor-pointer hover:shadow-md ${
                              selectedFieldIndex === index 
                                ? 'border-primary bg-primary/5' 
                                : 'border-border hover:border-primary/50'
                            }`}
                            onClick={() => setSelectedFieldIndex(index)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex items-center gap-3">
                                <div className="p-2 rounded-lg bg-primary/10">
                                  <Icon className="w-5 h-5 text-primary" />
                                </div>
                                <div>
                                  <h3 className="font-medium text-foreground">{field.label}</h3>
                                  <p className="text-sm text-muted-foreground">
                                    {field.placeholder || `${fieldTypeLabels[field.type]} field`}
                                  </p>
                                  {field.required && (
                                    <Badge variant="secondary" className="text-xs mt-1">
                                      Required
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeField(index);
                                }}
                                className="opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <Trash2 className="w-4 h-4 text-destructive" />
                              </Button>
                            </div>
                          </div>
                        );
                      })
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="theme" className="flex-1 overflow-y-auto p-6">
                <div className="max-w-2xl mx-auto space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Theme & Styling</CardTitle>
                      <CardDescription>Customize the visual appearance of your form</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Colors */}
                      <div className="space-y-4">
                        <h4 className="font-medium">Colors</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <ColorPicker
                            label="Primary Color"
                            value={formTemplate.theme.primary_color || '#3b82f6'}
                            onChange={(color) => setFormTemplate(prev => ({
                              ...prev,
                              theme: { ...prev.theme, primary_color: color }
                            }))}
                          />
                          <ColorPicker
                            label="Secondary Color"
                            value={formTemplate.theme.secondary_color || '#64748b'}
                            onChange={(color) => setFormTemplate(prev => ({
                              ...prev,
                              theme: { ...prev.theme, secondary_color: color }
                            }))}
                          />
                          <ColorPicker
                            label="Background Color"
                            value={formTemplate.theme.background_color || '#ffffff'}
                            onChange={(color) => setFormTemplate(prev => ({
                              ...prev,
                              theme: { ...prev.theme, background_color: color }
                            }))}
                          />
                          <ColorPicker
                            label="Text Color"
                            value={formTemplate.theme.text_color || '#1f2937'}
                            onChange={(color) => setFormTemplate(prev => ({
                              ...prev,
                              theme: { ...prev.theme, text_color: color }
                            }))}
                          />
                        </div>
                      </div>

                      <Separator />

                      {/* Typography */}
                      <div className="space-y-4">
                        <h4 className="font-medium">Typography</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Font Family</Label>
                            <Select
                              value={formTemplate.theme.font_family || 'Inter, sans-serif'}
                              onValueChange={(value) => setFormTemplate(prev => ({
                                ...prev,
                                theme: { ...prev.theme, font_family: value }
                              }))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Inter, sans-serif">Inter</SelectItem>
                                <SelectItem value="Roboto, sans-serif">Roboto</SelectItem>
                                <SelectItem value="Open Sans, sans-serif">Open Sans</SelectItem>
                                <SelectItem value="Lato, sans-serif">Lato</SelectItem>
                                <SelectItem value="Poppins, sans-serif">Poppins</SelectItem>
                                <SelectItem value="Montserrat, sans-serif">Montserrat</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>Font Size</Label>
                            <Input
                              type="number"
                              value={formTemplate.theme.font_size || 16}
                              onChange={(e) => setFormTemplate(prev => ({
                                ...prev,
                                theme: { ...prev.theme, font_size: parseInt(e.target.value) }
                              }))}
                              min="12"
                              max="24"
                            />
                          </div>
                        </div>
                      </div>

                      <Separator />

                      {/* Layout */}
                      <div className="space-y-4">
                        <h4 className="font-medium">Layout</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Border Radius</Label>
                            <Input
                              type="number"
                              value={formTemplate.theme.border_radius || 8}
                              onChange={(e) => setFormTemplate(prev => ({
                                ...prev,
                                theme: { ...prev.theme, border_radius: parseInt(e.target.value) }
                              }))}
                              min="0"
                              max="20"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Spacing</Label>
                            <Input
                              type="number"
                              value={formTemplate.theme.spacing || 16}
                              onChange={(e) => setFormTemplate(prev => ({
                                ...prev,
                                theme: { ...prev.theme, spacing: parseInt(e.target.value) }
                              }))}
                              min="8"
                              max="32"
                            />
                          </div>
                        </div>
                      </div>

                      <Separator />

                      {/* Branding */}
                      <div className="space-y-4">
                        <h4 className="font-medium">Branding</h4>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label>Logo URL</Label>
                            <Input
                              value={formTemplate.theme.logo_url || ''}
                              onChange={(e) => setFormTemplate(prev => ({
                                ...prev,
                                theme: { ...prev.theme, logo_url: e.target.value }
                              }))}
                              placeholder="https://example.com/logo.png"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Logo Position</Label>
                            <Select
                              value={formTemplate.theme.logo_position || 'top'}
                              onValueChange={(value) => setFormTemplate(prev => ({
                                ...prev,
                                theme: { ...prev.theme, logo_position: value as any }
                              }))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="top">Top</SelectItem>
                                <SelectItem value="bottom">Bottom</SelectItem>
                                <SelectItem value="left">Left</SelectItem>
                                <SelectItem value="right">Right</SelectItem>
                                <SelectItem value="center">Center</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="settings" className="flex-1 overflow-y-auto p-6">
                <div className="max-w-2xl mx-auto space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Form Settings</CardTitle>
                      <CardDescription>Configure your form's behavior and appearance</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label>Form Name</Label>
                        <Input
                          value={formTemplate.name}
                          onChange={(e) => setFormTemplate(prev => ({ ...prev, name: e.target.value }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Description</Label>
                        <Textarea
                          value={formTemplate.description}
                          onChange={(e) => setFormTemplate(prev => ({ ...prev, description: e.target.value }))}
                        />
                      </div>

                      <Separator />

                      {/* Form Behavior */}
                      <div className="space-y-4">
                        <h4 className="font-medium">Form Behavior</h4>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label>Show Progress Bar</Label>
                              <p className="text-sm text-muted-foreground">Display progress indicator</p>
                            </div>
                            <Switch
                              checked={formTemplate.settings.show_progress_bar}
                              onCheckedChange={(checked) => setFormTemplate(prev => ({
                                ...prev,
                                settings: { ...prev.settings, show_progress_bar: checked }
                              }))}
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label>Enable Booking</Label>
                              <p className="text-sm text-muted-foreground">Allow appointment scheduling</p>
                            </div>
                            <Switch
                              checked={formTemplate.settings.enable_booking}
                              onCheckedChange={(checked) => setFormTemplate(prev => ({
                                ...prev,
                                settings: { ...prev.settings, enable_booking: checked }
                              }))}
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label>Show Pricing</Label>
                              <p className="text-sm text-muted-foreground">Display service pricing</p>
                            </div>
                            <Switch
                              checked={formTemplate.settings.show_pricing}
                              onCheckedChange={(checked) => setFormTemplate(prev => ({
                                ...prev,
                                settings: { ...prev.settings, show_pricing: checked }
                              }))}
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label>Allow Save Draft</Label>
                              <p className="text-sm text-muted-foreground">Let users save progress</p>
                            </div>
                            <Switch
                              checked={formTemplate.settings.allow_save_draft || false}
                              onCheckedChange={(checked) => setFormTemplate(prev => ({
                                ...prev,
                                settings: { ...prev.settings, allow_save_draft: checked }
                              }))}
                            />
                          </div>
                        </div>
                      </div>

                      <Separator />

                      {/* Navigation */}
                      <div className="space-y-4">
                        <h4 className="font-medium">Navigation</h4>
                        <div className="space-y-2">
                          <Label>Navigation Type</Label>
                          <Select
                            value={formTemplate.settings.navigation_type}
                            onValueChange={(value) => setFormTemplate(prev => ({
                              ...prev,
                              settings: { ...prev.settings, navigation_type: value as 'vertical' | 'horizontal' }
                            }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="vertical">Vertical</SelectItem>
                              <SelectItem value="horizontal">Horizontal</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <Separator />

                      {/* Success Message */}
                      <div className="space-y-2">
                        <Label>Success Message</Label>
                        <Textarea
                          value={formTemplate.settings.success_message}
                          onChange={(e) => setFormTemplate(prev => ({
                            ...prev,
                            settings: { ...prev.settings, success_message: e.target.value }
                          }))}
                          placeholder="Thank you for your submission!"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="integrations" className="flex-1 overflow-y-auto p-6">
                <div className="max-w-2xl mx-auto space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Integrations</CardTitle>
                      <CardDescription>Connect your form with external services</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Email Integration */}
                      <div className="space-y-4">
                        <h4 className="font-medium">Email Integration</h4>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label>Email Provider</Label>
                            <Select
                              value={formTemplate.integrations?.email_provider || ''}
                              onValueChange={(value) => setFormTemplate(prev => ({
                                ...prev,
                                integrations: {
                                  ...prev.integrations,
                                  email_provider: value as any
                                }
                              }))}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select email provider" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="sendgrid">SendGrid</SelectItem>
                                <SelectItem value="mailchimp">Mailchimp</SelectItem>
                                <SelectItem value="constant_contact">Constant Contact</SelectItem>
                                <SelectItem value="custom">Custom</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label>Send Confirmation Email</Label>
                              <p className="text-sm text-muted-foreground">Send email to form submitters</p>
                            </div>
                            <Switch
                              checked={formTemplate.settings.send_confirmation_email || false}
                              onCheckedChange={(checked) => setFormTemplate(prev => ({
                                ...prev,
                                settings: { ...prev.settings, send_confirmation_email: checked }
                              }))}
                            />
                          </div>
                        </div>
                      </div>

                      <Separator />

                      {/* Webhook Integration */}
                      <div className="space-y-4">
                        <h4 className="font-medium">Webhook</h4>
                        <div className="space-y-2">
                          <Label>Webhook URL</Label>
                          <Input
                            value={formTemplate.settings.webhook_url || ''}
                            onChange={(e) => setFormTemplate(prev => ({
                              ...prev,
                              settings: { ...prev.settings, webhook_url: e.target.value }
                            }))}
                            placeholder="https://your-webhook-url.com/endpoint"
                          />
                        </div>
                      </div>

                      <Separator />

                      {/* Calendar Integration */}
                      <div className="space-y-4">
                        <h4 className="font-medium">Calendar Integration</h4>
                        <div className="space-y-2">
                          <Label>Calendar Provider</Label>
                          <Select
                            value={formTemplate.integrations?.calendar_provider || ''}
                            onValueChange={(value) => setFormTemplate(prev => ({
                              ...prev,
                              integrations: {
                                ...prev.integrations,
                                calendar_provider: value as any
                              }
                            }))}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select calendar provider" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="google">Google Calendar</SelectItem>
                              <SelectItem value="outlook">Outlook</SelectItem>
                              <SelectItem value="calendly">Calendly</SelectItem>
                              <SelectItem value="custom">Custom</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Panel - Field Properties */}
          {selectedFieldIndex !== null && (
            <div className="w-80 bg-card border-l border-border flex flex-col">
              <div className="p-6 border-b border-border">
                <h2 className="text-lg font-semibold text-foreground">Field Properties</h2>
                <p className="text-sm text-muted-foreground mt-1">Configure the selected field</p>
              </div>
              <div className="flex-1 overflow-y-auto p-4">
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">Basic</TabsTrigger>
                    <TabsTrigger value="validation">Validation</TabsTrigger>
                    <TabsTrigger value="styling">Styling</TabsTrigger>
                    <TabsTrigger value="logic">Logic</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4 mt-4">
                    {/* Common Properties */}
                    <div>
                      <Label htmlFor="field-label">Label</Label>
                      <Input
                        id="field-label"
                        value={currentPage.fields[selectedFieldIndex].label}
                        onChange={(e) => updateField(selectedFieldIndex, { label: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="field-placeholder">Placeholder</Label>
                      <Input
                        id="field-placeholder"
                        value={currentPage.fields[selectedFieldIndex].placeholder || ''}
                        onChange={(e) => updateField(selectedFieldIndex, { placeholder: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="field-help-text">Help Text</Label>
                      <Textarea
                        id="field-help-text"
                        value={currentPage.fields[selectedFieldIndex].help_text || ''}
                        onChange={(e) => updateField(selectedFieldIndex, { help_text: e.target.value })}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="field-required"
                        checked={currentPage.fields[selectedFieldIndex].required}
                        onCheckedChange={(checked) => updateField(selectedFieldIndex, { required: checked })}
                      />
                      <Label htmlFor="field-required">Required</Label>
                    </div>

                    {/* Field Width */}
                    <div>
                      <Label htmlFor="field-width">Width</Label>
                      <Select
                        value={currentPage.fields[selectedFieldIndex].width || 'full'}
                        onValueChange={(value) => updateField(selectedFieldIndex, { width: value as 'full' | 'half' | 'third' | 'quarter' })}
                      >
                        <SelectTrigger id="field-width">
                          <SelectValue placeholder="Select width" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="full">Full</SelectItem>
                          <SelectItem value="half">Half</SelectItem>
                          <SelectItem value="third">Third</SelectItem>
                          <SelectItem value="quarter">Quarter</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </TabsContent>

                  <TabsContent value="validation" className="space-y-4 mt-4">
                    {/* Validation Rules */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Validation Rules</h4>

                      {(currentPage.fields[selectedFieldIndex].type === 'short_text' ||
                        currentPage.fields[selectedFieldIndex].type === 'long_text') && (
                        <>
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <Label htmlFor="min-length">Min Length</Label>
                              <Input
                                id="min-length"
                                type="number"
                                value={currentPage.fields[selectedFieldIndex].validation?.min_length || ''}
                                onChange={(e) => updateField(selectedFieldIndex, {
                                  validation: {
                                    ...currentPage.fields[selectedFieldIndex].validation,
                                    min_length: parseInt(e.target.value) || undefined
                                  }
                                })}
                              />
                            </div>
                            <div>
                              <Label htmlFor="max-length">Max Length</Label>
                              <Input
                                id="max-length"
                                type="number"
                                value={currentPage.fields[selectedFieldIndex].validation?.max_length || ''}
                                onChange={(e) => updateField(selectedFieldIndex, {
                                  validation: {
                                    ...currentPage.fields[selectedFieldIndex].validation,
                                    max_length: parseInt(e.target.value) || undefined
                                  }
                                })}
                              />
                            </div>
                          </div>
                        </>
                      )}

                      <div>
                        <Label htmlFor="custom-regex">Custom Validation Pattern</Label>
                        <Input
                          id="custom-regex"
                          value={currentPage.fields[selectedFieldIndex].validation?.custom_regex || ''}
                          onChange={(e) => updateField(selectedFieldIndex, {
                            validation: {
                              ...currentPage.fields[selectedFieldIndex].validation,
                              custom_regex: e.target.value
                            }
                          })}
                          placeholder="^[A-Za-z0-9]+$"
                        />
                      </div>

                      <div>
                        <Label htmlFor="custom-message">Custom Error Message</Label>
                        <Input
                          id="custom-message"
                          value={currentPage.fields[selectedFieldIndex].validation?.custom_message || ''}
                          onChange={(e) => updateField(selectedFieldIndex, {
                            validation: {
                              ...currentPage.fields[selectedFieldIndex].validation,
                              custom_message: e.target.value
                            }
                          })}
                          placeholder="Please enter a valid value"
                        />
                      </div>

                      {currentPage.fields[selectedFieldIndex].type === 'file_upload' && (
                        <>
                          <div>
                            <Label htmlFor="file-types">Allowed File Types</Label>
                            <Input
                              id="file-types"
                              value={currentPage.fields[selectedFieldIndex].validation?.file_types?.join(', ') || ''}
                              onChange={(e) => updateField(selectedFieldIndex, {
                                validation: {
                                  ...currentPage.fields[selectedFieldIndex].validation,
                                  file_types: e.target.value.split(',').map(type => type.trim())
                                }
                              })}
                              placeholder="pdf, jpg, png, doc"
                            />
                          </div>
                          <div>
                            <Label htmlFor="max-file-size">Max File Size (MB)</Label>
                            <Input
                              id="max-file-size"
                              type="number"
                              value={currentPage.fields[selectedFieldIndex].validation?.max_file_size || ''}
                              onChange={(e) => updateField(selectedFieldIndex, {
                                validation: {
                                  ...currentPage.fields[selectedFieldIndex].validation,
                                  max_file_size: parseInt(e.target.value) || undefined
                                }
                              })}
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="multiple-files"
                              checked={currentPage.fields[selectedFieldIndex].validation?.multiple_files || false}
                              onCheckedChange={(checked) => updateField(selectedFieldIndex, {
                                validation: {
                                  ...currentPage.fields[selectedFieldIndex].validation,
                                  multiple_files: checked
                                }
                              })}
                            />
                            <Label htmlFor="multiple-files">Allow Multiple Files</Label>
                          </div>
                        </>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="styling" className="space-y-4 mt-4">
                    {/* Field Styling */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Field Styling</h4>

                      <div className="grid grid-cols-2 gap-2">
                        <ColorPicker
                          label="Background Color"
                          value={currentPage.fields[selectedFieldIndex].styling?.background_color || '#ffffff'}
                          onChange={(color) => updateField(selectedFieldIndex, {
                            styling: {
                              ...currentPage.fields[selectedFieldIndex].styling,
                              background_color: color
                            }
                          })}
                        />
                        <ColorPicker
                          label="Text Color"
                          value={currentPage.fields[selectedFieldIndex].styling?.text_color || '#000000'}
                          onChange={(color) => updateField(selectedFieldIndex, {
                            styling: {
                              ...currentPage.fields[selectedFieldIndex].styling,
                              text_color: color
                            }
                          })}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="font-size">Font Size</Label>
                          <Input
                            id="font-size"
                            type="number"
                            value={currentPage.fields[selectedFieldIndex].styling?.font_size || ''}
                            onChange={(e) => updateField(selectedFieldIndex, {
                              styling: {
                                ...currentPage.fields[selectedFieldIndex].styling,
                                font_size: parseInt(e.target.value) || undefined
                              }
                            })}
                            min="12"
                            max="24"
                          />
                        </div>
                        <div>
                          <Label htmlFor="border-radius">Border Radius</Label>
                          <Input
                            id="border-radius"
                            type="number"
                            value={currentPage.fields[selectedFieldIndex].styling?.border_radius || ''}
                            onChange={(e) => updateField(selectedFieldIndex, {
                              styling: {
                                ...currentPage.fields[selectedFieldIndex].styling,
                                border_radius: parseInt(e.target.value) || undefined
                              }
                            })}
                            min="0"
                            max="20"
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="font-weight">Font Weight</Label>
                        <Select
                          value={currentPage.fields[selectedFieldIndex].styling?.font_weight || 'normal'}
                          onValueChange={(value) => updateField(selectedFieldIndex, {
                            styling: {
                              ...currentPage.fields[selectedFieldIndex].styling,
                              font_weight: value as 'normal' | 'bold' | 'light'
                            }
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="light">Light</SelectItem>
                            <SelectItem value="normal">Normal</SelectItem>
                            <SelectItem value="bold">Bold</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="custom-css">Custom CSS</Label>
                        <Textarea
                          id="custom-css"
                          value={currentPage.fields[selectedFieldIndex].styling?.custom_css || ''}
                          onChange={(e) => updateField(selectedFieldIndex, {
                            styling: {
                              ...currentPage.fields[selectedFieldIndex].styling,
                              custom_css: e.target.value
                            }
                          })}
                          placeholder="border: 1px solid #ccc; padding: 10px;"
                          rows={3}
                        />
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="logic" className="space-y-4 mt-4">
                    {/* Conditional Logic */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Conditional Logic</h4>
                      <p className="text-sm text-muted-foreground">
                        Show or require this field based on other field values
                      </p>

                      <div className="space-y-4">
                        <div>
                          <Label>Show this field when:</Label>
                          <div className="mt-2 p-4 border rounded-lg bg-muted/50">
                            <p className="text-sm text-muted-foreground">
                              Conditional logic configuration will be available in a future update.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                    </div>
            </div>
          )}
        </div>
      </div>
    );
  }
);

FormBuilder.displayName = 'FormBuilder';
