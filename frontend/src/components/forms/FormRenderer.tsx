'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Wrench, ChevronLeft, ChevronRight } from 'lucide-react';
import { FormField } from './fields/FormField';
import { type FormTemplate, type FormPage, type FormField as FormFieldType } from '@/data/services';
import { cn } from '@/lib/utils';

interface FormRendererProps {
  template: FormTemplate;
  onSubmit: (data: Record<string, any>) => void;
  onSave?: (data: Record<string, any>) => void;
  customProps?: Record<string, any>;
  className?: string;
}

export function FormRenderer({ template, onSubmit, onSave, customProps }: FormRendererProps) {
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const pageRefs = useRef<(HTMLDivElement | null)[]>([]);

  const currentPage = template.pages[currentPageIndex];
  const isLastPage = currentPageIndex === template.pages.length - 1;
  const progress = ((currentPageIndex + 1) / template.pages.length) * 100;

  const updateFormData = (fieldId: string, value: any) => {
    setFormData((prev) => ({ ...prev, [fieldId]: value }));
  };

  const validateCurrentPage = () => {
    const requiredFields = currentPage.fields.filter((field) => field.required);
    return requiredFields.every((field) => {
      const value = formData[field.id];
      return value !== undefined && value !== null && value !== '';
    });
  };

  const scrollToPage = (pageIndex: number) => {
    const pageElement = pageRefs.current[pageIndex];
    if (pageElement) {
      pageElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  const handleNext = () => {
    if (!validateCurrentPage()) {
      // Show validation errors
      return;
    }

    if (isLastPage) {
      handleSubmit();
    } else {
      const nextIndex = currentPageIndex + 1;
      setCurrentPageIndex(nextIndex);
      scrollToPage(nextIndex);
    }
  };

  const handleBack = () => {
    if (currentPageIndex > 0) {
      const prevIndex = currentPageIndex - 1;
      setCurrentPageIndex(prevIndex);
      scrollToPage(prevIndex);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      setIsComplete(true);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-scroll to current page when it changes
  useEffect(() => {
    scrollToPage(currentPageIndex);
  }, [currentPageIndex]);

  if (isComplete) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="max-w-md text-center"
        >
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-10 h-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-4">Request Submitted!</h1>
          <p className="text-muted-foreground mb-8">{template.settings.success_message}</p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-gradient-to-r from-green-600 to-blue-600 text-white"
          >
            Submit Another Request
          </Button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Fixed Header with Progress */}
      <div className="fixed top-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-b z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-xl font-bold text-foreground">{template.name}</h1>
            <div className="text-sm text-muted-foreground">
              Step {currentPageIndex + 1} of {template.pages.length}
            </div>
          </div>
          {template.settings.show_progress_bar && <Progress value={progress} className="h-2" />}
        </div>
      </div>

      {/* Form Content */}
      <div className="pt-24 pb-8">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto space-y-8">
            {template.pages.map((page, pageIndex) => (
              <motion.div
                key={page.id}
                ref={(el) => (pageRefs.current[pageIndex] = el)}
                initial={{ opacity: 0.3, scale: 0.95 }}
                animate={{
                  opacity: pageIndex === currentPageIndex ? 1 : 0.3,
                  scale: pageIndex === currentPageIndex ? 1 : 0.95,
                }}
                transition={{ duration: 0.3 }}
                className="min-h-screen flex items-center"
              >
                <Card className="w-full shadow-xl">
                  <CardHeader className="text-center pb-6">
                    <CardTitle className="text-2xl font-bold text-foreground mb-2">
                      {page.title}
                    </CardTitle>
                    {page.description && (
                      <p className="text-muted-foreground text-lg">{page.description}</p>
                    )}
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {page.fields.map((field) => (
                      <FormField
                        key={field.id}
                        field={field}
                        value={formData[field.id]}
                        onChange={(value) => updateFormData(field.id, value)}
                        formData={formData}
                        customProps={customProps}
                      />
                    ))}

                    {/* Navigation Buttons */}
                    <div className="flex gap-4 pt-6">
                      {pageIndex > 0 && (
                        <Button
                          variant="outline"
                          onClick={handleBack}
                          className="flex items-center gap-2"
                        >
                          <ArrowUp className="w-4 h-4" />
                          Back
                        </Button>
                      )}
                      <Button
                        onClick={handleNext}
                        disabled={!validateCurrentPage() || isSubmitting}
                        className="flex-1 flex items-center justify-center gap-2"
                        variant="gradient"
                      >
                        {isSubmitting ? (
                          'Submitting...'
                        ) : isLastPage ? (
                          page.button_text || 'Submit'
                        ) : (
                          <>
                            {page.button_text || 'Continue'}
                            <ArrowDown className="w-4 h-4" />
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Floating Navigation */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40 flex flex-col">
        {template.pages.map((_, index) => (
          <button
            key={index}
            onClick={() => {
              setCurrentPageIndex(index);
              scrollToPage(index);
            }}
            className={`w-3 h-3 rounded-full transition-all mb-2 last:mb-0 cursor-pointer ${
              index === currentPageIndex
                ? 'bg-primary scale-125'
                : index < currentPageIndex
                  ? 'bg-success'
                  : 'bg-muted-foreground'
            }`}
          />
        ))}
      </div>
    </div>
  );
}
