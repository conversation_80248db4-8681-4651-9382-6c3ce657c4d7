# Professional Home Service Form Builder System

A complete form builder system specifically designed for home service companies, featuring template-based form creation, vertical scrolling navigation, appointment booking, and professional UI components.

## 🎯 System Overview

This is a comprehensive form builder that allows home service companies to create professional booking forms with:

- **Template-based creation** - Choose from pre-built templates or start from scratch
- **Real form customization** - Each template has unique fields and flows
- **Vertical scroll navigation** - Smooth scrolling between form steps
- **Appointment booking** - Integrated calendar and time slot selection
- **Professional UI** - Modern design with animations and responsive layout
- **Backend integration** - Full API support for form management and submissions

## Features

### 🎯 Multi-Step Form Flow

- **Step 1**: Service Category Selection (Plumbing, Heating & Cooling, Electrical)
- **Step 2**: Service Type Selection (Repair, Install, Maintenance, Other)
- **Step 3**: Specific Service Options with custom descriptions
- **Step 4**: Address Input with Google Maps-style autocomplete
- **Step 5**: Confirmation and review

### 🎨 Modern UI/UX

- Clean, professional design with shadcn/ui components
- Smooth animations powered by Framer Motion
- Responsive design that works on all devices
- Progress indicator with step navigation
- Hover states and interactive feedback

### 🗺️ Address Autocomplete

- Google Maps-style address suggestions
- Structured address parsing (street, city, state, ZIP)
- Location notes for special instructions
- Keyboard navigation support

### 📋 Template System

- Pre-built templates for common service types
- "Start from scratch" option for custom forms
- Template preview functionality
- Easy template selection and customization

### 🔧 Form Management

- Live preview with device responsive testing
- Form settings configuration
- Embed code generation
- Analytics and performance tracking

## 🏗️ System Architecture

### Form Templates

The system includes three main templates with different flows:

1. **Complete Service Request Form** (6 pages)
   - Page 1: ZIP Code location
   - Page 2: Contact information (name, phone, email)
   - Page 3: Service address with Google Maps autocomplete
   - Page 4: Service selection (category → type → description)
   - Page 5: Appointment slot selection with calendar view
   - Page 6: Summary with pricing, terms, and file upload

2. **Quick Quote Request** (3 pages)
   - Page 1: Service category and project description
   - Page 2: Contact information and location
   - Page 3: Timeline and contact preferences

3. **Emergency Service Request** (2 pages)
   - Page 1: Emergency type and description with contact
   - Page 2: Location and access instructions

### Form Builder Components

#### `FormBuilder`

Complete drag-and-drop form builder interface with:

- Field palette with 20+ field types
- Visual form canvas with page management
- Field property editor
- Live preview mode
- Form settings configuration

#### `FormRenderer`

Renders forms with vertical scrolling navigation:

- Smooth scroll between pages
- Progress indicator
- Floating navigation dots
- Responsive design
- Form validation

#### `TemplateSelector`

Professional template selection interface:

- Template categories and ratings
- Usage statistics
- Preview functionality
- "Start from scratch" option

## Components

### Core Components

#### `ServiceBookingForm`

Main orchestrator component that manages the multi-step flow.

```tsx
import { ServiceBookingForm } from '@/components/forms/service-booking';

<ServiceBookingForm
  onComplete={(data) => console.log(data)}
  initialData={{ categoryId: 'plumbing' }}
/>;
```

#### `CategorySelector`

Displays service categories with icons and descriptions.

#### `SubCategorySelector`

Shows service types (Repair, Install, etc.) for selected category.

#### `OptionSelector`

Presents specific service options with custom description input.

#### `AddressStep`

Address input with autocomplete and location details.

#### `ConfirmationScreen`

Final review screen showing all collected information.

### Specialized Field Components

#### `FormField`

Universal field renderer that handles all field types:

- Basic fields (text, email, phone, etc.)
- Home service specific fields
- File uploads and ratings
- Conditional logic support

#### `ServiceCategoryField`

Service category selection with icons and descriptions.

#### `AppointmentSlotsField`

Appointment booking with calendar view:

- Available time slots
- Pricing display
- Duration and service type filtering

#### `PricingDisplayField`

Professional pricing breakdown:

- Service fees and dispatch costs
- Payment terms and guarantees
- Financing options

#### `AddressAutocomplete`

Google Maps-style address input with suggestions.

## Data Structure

### Service Hierarchy

```typescript
interface ServiceCategory {
  id: string;
  name: string;
  icon: LucideIcon;
  description: string;
  color: string;
  subCategories: ServiceSubCategory[];
}

interface ServiceSubCategory {
  id: string;
  name: string;
  icon: LucideIcon;
  options: ServiceOption[];
}

interface ServiceOption {
  id: string;
  name: string;
  description?: string;
}
```

### Form Data

```typescript
interface BookingData {
  categoryId?: string;
  subCategoryId?: string;
  optionId?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  locationNotes?: string;
  // Additional fields...
}
```

## Usage

### Basic Implementation

```tsx
import { ServiceBookingForm } from '@/components/forms/service-booking';

function MyBookingPage() {
  const handleFormComplete = (data: BookingData) => {
    // Process form submission
    console.log('Form completed:', data);
  };

  return <ServiceBookingForm onComplete={handleFormComplete} />;
}
```

### With Template Selection

```tsx
import { TemplateSelector } from '@/components/forms/TemplateSelector';
import { ServiceBookingForm } from '@/components/forms/service-booking';

function FormCreationPage() {
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showForm, setShowForm] = useState(false);

  if (!showForm) {
    return (
      <TemplateSelector
        onTemplateSelect={(template) => {
          setSelectedTemplate(template);
          setShowForm(true);
        }}
        onStartFromScratch={() => setShowForm(true)}
      />
    );
  }

  return (
    <ServiceBookingForm
      initialData={selectedTemplate?.defaultServices?.[0]}
      onComplete={handleFormComplete}
    />
  );
}
```

## Configuration

### Environment Variables

```env
NEXT_PUBLIC_BACKEND_URL=http://localhost:3000
```

### Customization

- Service categories and options are defined in `/src/data/services.ts`
- Form templates are configured in the same file
- Styling can be customized through Tailwind CSS classes
- Icons are from Lucide React

## API Integration

The form system is designed to work with the backend web forms API:

- `POST /api/web-forms` - Create new form
- `GET /api/web-forms` - List forms
- `POST /api/web-forms/{id}/submissions` - Submit form data

## Accessibility

- Keyboard navigation support
- ARIA labels and descriptions
- Focus management
- Screen reader friendly
- High contrast support

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance

- Lazy loading of components
- Optimized animations
- Minimal bundle size
- Fast form validation
- Efficient re-renders
