"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { services } from "@/data/services";

interface ServiceCategoryFieldProps {
  value: string;
  onChange: (value: string) => void;
  categories?: string[];
}

export function ServiceCategoryField({ value, onChange, categories }: ServiceCategoryFieldProps) {
  const availableServices = categories 
    ? services.filter(service => categories.includes(service.id))
    : services;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {availableServices.map((service) => {
        const Icon = service.icon;
        const isSelected = value === service.id;
        
        return (
          <motion.div
            key={service.id}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <Card
              className={cn(
                "cursor-pointer transition-all duration-300 hover:shadow-md border-2",
                isSelected
                  ? "border-primary bg-accent shadow-sm"
                  : "border-border hover:border-primary/30"
              )}
              onClick={() => onChange(service.id)}
            >
              <CardContent className="p-6 text-center">
                <div className={cn(
                  "p-3 rounded-lg bg-background mx-auto mb-3 w-fit shadow-sm",
                  service.color
                )}>
                  <Icon className="w-6 h-6" />
                </div>
                <h3 className="font-semibold text-foreground mb-1">
                  {service.name}
                </h3>
                <p className="text-xs text-muted-foreground">
                  {service.description}
                </p>
              </CardContent>
            </Card>
          </motion.div>
        );
      })}
    </div>
  );
}
