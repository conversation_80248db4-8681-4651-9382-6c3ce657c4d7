'use client';

import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { AddressAutocomplete } from '../AddressAutocomplete';
import { ServiceCategoryField } from './ServiceCategoryField';
import { AppointmentSlotsField } from './AppointmentSlotsField';
import { PricingDisplayField } from './PricingDisplayField';
import { ServiceTypeField } from './ServiceTypeField';
import { ServiceOptionsField } from './ServiceOptionsField';
import { CalendarField } from './CalendarField';
import { type FormField as FormFieldType } from '@/data/services';
import { Upload, Star } from 'lucide-react';
import { ZipCodeField } from './ZipCodeField';

interface FormFieldProps {
  field: FormFieldType;
  value: any;
  onChange: (value: any) => void;
  formData: Record<string, any>;
  customProps?: Record<string, any>;
}

export function FormField({ field, value, onChange, formData, customProps }: FormFieldProps) {
  const renderField = () => {
    switch (field.type) {
      case 'short_text':
        return (
          <Input
            type="text"
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="w-full"
          />
        );

      case 'long_text':
        return (
          <Textarea
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="w-full min-h-24"
            rows={4}
          />
        );

      case 'email':
        return (
          <Input
            type="email"
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="w-full"
          />
        );

      case 'phone':
        return (
          <Input
            type="tel"
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="w-full"
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => onChange(Number(e.target.value))}
            min={field.min_value}
            max={field.max_value}
            className="w-full"
          />
        );

      case 'zip_code':
        return (
          <ZipCodeField
            value={value || ''}
            onChange={onChange}
            placeholder={field.placeholder}
            className="w-full"
            companyName={customProps?.zip_code?.companyName}
            companyAddress={customProps?.zip_code?.companyAddress}
            {...customProps?.zip_code}
          />
        );

      case 'address':
        return (
          <AddressAutocomplete
            value={value || ''}
            onChange={onChange}
            placeholder={field.placeholder}
            className="w-full"
          />
        );

      case 'dropdown':
        return (
          <Select value={value || ''} onValueChange={onChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder={field.placeholder || 'Select an option'} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'multiple_choice':
        return (
          <RadioGroup value={value || ''} onValueChange={onChange}>
            <div className="space-y-4">
              {field.options?.map((option) => (
                <div
                  key={option}
                  className="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 hover:border-primary/30 transition-colors cursor-pointer"
                >
                  <RadioGroupItem value={option} id={`${field.id}-${option}`} className="mt-0.5" />
                  <Label
                    htmlFor={`${field.id}-${option}`}
                    className="cursor-pointer flex-1 text-sm leading-relaxed"
                  >
                    {option}
                  </Label>
                </div>
              ))}
            </div>
          </RadioGroup>
        );

      case 'checkboxes':
        const selectedValues = value || [];
        return (
          <div className="space-y-3">
            {field.options?.map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <Checkbox
                  id={`${field.id}-${option}`}
                  checked={selectedValues.includes(option)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      onChange([...selectedValues, option]);
                    } else {
                      onChange(selectedValues.filter((v: string) => v !== option));
                    }
                  }}
                />
                <Label htmlFor={`${field.id}-${option}`} className="cursor-pointer">
                  {option}
                </Label>
              </div>
            ))}
          </div>
        );

      case 'date':
        return (
          <Input
            type="date"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="w-full"
          />
        );

      case 'time':
        return (
          <Input
            type="time"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="w-full"
          />
        );

      case 'file_upload':
        return (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
            <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600 mb-2">Click to upload or drag and drop</p>
            <p className="text-xs text-gray-500">PNG, JPG, PDF up to 10MB</p>
            <input
              type="file"
              multiple
              className="hidden"
              onChange={(e) => {
                const files = Array.from(e.target.files || []);
                onChange(files);
              }}
            />
          </div>
        );

      case 'rating':
        return (
          <div className="flex items-center gap-1">
            {[1, 2, 3, 4, 5].map((rating) => (
              <button key={rating} type="button" onClick={() => onChange(rating)} className="p-1">
                <Star
                  className={cn(
                    'w-6 h-6 transition-colors',
                    rating <= (value || 0) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300',
                  )}
                />
              </button>
            ))}
          </div>
        );

      case 'yes_no':
        return (
          <RadioGroup value={value || ''} onValueChange={onChange}>
            <div className="flex gap-6">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id={`${field.id}-yes`} />
                <Label htmlFor={`${field.id}-yes`}>Yes</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id={`${field.id}-no`} />
                <Label htmlFor={`${field.id}-no`}>No</Label>
              </div>
            </div>
          </RadioGroup>
        );

      case 'website':
        return (
          <Input
            type="url"
            placeholder={field.placeholder || 'https://example.com'}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="w-full"
          />
        );

      case 'legal':
        return (
          <div className="flex items-start space-x-2">
            <Checkbox id={field.id} checked={value || false} onCheckedChange={onChange} />
            <Label htmlFor={field.id} className="text-sm leading-relaxed cursor-pointer">
              {field.label}
            </Label>
          </div>
        );

      case 'payment':
        return (
          <div className="p-4 border border-dashed border-blue-300 rounded-lg text-center text-blue-700 bg-blue-50">
            <DollarSign className="w-8 h-8 mx-auto mb-2" />
            <p className="text-sm font-medium">Payment Integration Placeholder</p>
            <p className="text-xs">This field would integrate with a payment gateway.</p>
          </div>
        );

      case 'service_category':
        return (
          <ServiceCategoryField
            value={value}
            onChange={onChange}
            categories={field.service_categories}
          />
        );

      case 'service_type':
        return (
          <ServiceTypeField
            value={value}
            onChange={onChange}
            formData={formData}
          />
        );

      case 'service_options':
        return (
          <ServiceOptionsField
            value={value}
            onChange={onChange}
            formData={formData}
          />
        );

      case 'appointment_slots':
        return <AppointmentSlotsField value={value} onChange={onChange} formData={formData} />;

      case 'calendar_view':
        return (
          <CalendarField
            value={value}
            onChange={onChange}
          />
        );

      case 'pricing_display':
        return <PricingDisplayField formData={formData} />;

      default:
        return (
          <div className="p-4 border border-dashed border-gray-300 rounded-lg text-center text-gray-500">
            Field type "{field.type}" not implemented yet
          </div>
        );
    }
  };

  const getFieldWidth = () => {
    switch (field.width) {
      case 'half':
        return 'md:w-1/2';
      case 'third':
        return 'md:w-1/3';
      case 'quarter':
        return 'md:w-1/4';
      default:
        return 'w-full';
    }
  };

  return (
    <div className={cn('space-y-2', getFieldWidth())}>
      {field.type !== 'legal' && (
        <Label htmlFor={field.id} className="text-sm font-medium">
          {field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}

      {renderField()}

      {field.help_text && <p className="text-xs text-muted-foreground">{field.help_text}</p>}
    </div>
  );
}
