"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Calendar, Clock } from "lucide-react"; // Removed DollarSign

interface AppointmentSlot {
  id: string;
  date: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  is_available: boolean;
  base_price: number;
  dispatch_fee: number;
  service_types: string[];
}

interface AppointmentSlotsFieldProps {
  value: string;
  onChange: (value: string) => void;
  formData: Record<string, any>;
}

export function AppointmentSlotsField({ value, onChange, formData }: AppointmentSlotsFieldProps) {
  const [slots, setSlots] = useState<AppointmentSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<string | null>(null); // New state for selected date

  useEffect(() => {
    // Mock API call to fetch appointment slots
    const fetchSlots = async () => {
      setLoading(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Generate mock slots for the next 7 days
      const mockSlots: AppointmentSlot[] = [];
      const baseDate = new Date();
      baseDate.setHours(9, 0, 0, 0); // Start at 9 AM
      
      for (let day = 0; day < 7; day++) {
        const currentDate = new Date(baseDate);
        currentDate.setDate(baseDate.getDate() + day);
        
        // Skip weekends
        if (currentDate.getDay() === 0 || currentDate.getDay() === 6) continue;
        
        // Generate slots for each day (9 AM to 5 PM, 2-hour slots)
        for (let hour = 9; hour < 17; hour += 2) {
          const startTime = new Date(currentDate);
          startTime.setHours(hour, 0, 0, 0);
          
          const endTime = new Date(startTime);
          endTime.setHours(hour + 2, 0, 0, 0);
          
          mockSlots.push({
            id: `slot_${day}_${hour}`,
            date: currentDate.toISOString().split('T')[0],
            start_time: startTime.toTimeString().split(' ')[0].slice(0, 5),
            end_time: endTime.toTimeString().split(' ')[0].slice(0, 5),
            duration_minutes: 120,
            is_available: Math.random() > 0.3, // 70% availability
            base_price: 150,
            dispatch_fee: 75,
            service_types: ['plumbing', 'electrical', 'hvac']
          });
        }
      }
      
      setSlots(mockSlots);
      setLoading(false);
      if (mockSlots.length > 0) {
        setSelectedDate(mockSlots[0].date); // Select the first date by default
      }
    };

    fetchSlots();
  }, [formData.zip_code, formData.service_category]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Finding available appointments...</p>
        </div>
      </div>
    );
  }

  const availableSlots = slots.filter(slot => slot.is_available);
  const groupedSlots = availableSlots.reduce((groups, slot) => {
    const date = slot.date;
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(slot);
    return groups;
  }, {} as Record<string, AppointmentSlot[]>);

  const uniqueDates = Object.keys(groupedSlots).sort();

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Available Appointments</h3>

      {availableSlots.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No appointments available</h3>
            <p className="text-muted-foreground">
              Please try a different service area or contact us directly.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {/* Date Selector */}
          <div className="flex overflow-x-auto pb-2 -mx-4 px-4 no-scrollbar">
            {uniqueDates.map(date => (
              <Button
                key={date}
                variant="ghost"
                onClick={() => setSelectedDate(date)}
                className={cn(
                  "flex-shrink-0 mr-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors",
                  selectedDate === date
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "text-muted-foreground hover:bg-muted/50"
                )}
              >
                {formatDate(date)}
              </Button>
            ))}
          </div>

          {/* Time Slots for Selected Date */}
          {selectedDate && groupedSlots[selectedDate] && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  {formatDate(selectedDate)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {groupedSlots[selectedDate].map((slot) => (
                    <motion.div
                      key={slot.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        variant={value === slot.id ? "default" : "outline"}
                        onClick={() => onChange(slot.id)}
                        className={cn(
                          "w-full h-auto p-4 flex flex-col items-start gap-2",
                          value === slot.id && "bg-gradient-to-r from-blue-600 to-purple-600"
                        )}
                      >
                        <div className="flex items-center gap-2 text-sm font-medium">
                          
                          {formatTime(slot.start_time)} - {formatTime(slot.end_time)}
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {slot.duration_minutes} min
                        </Badge>
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {value && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-green-800">
              <Calendar className="w-4 h-4" />
              <span className="font-medium">Selected Appointment:</span>
            </div>
            {(() => {
              const selectedSlot = slots.find(s => s.id === value);
              if (!selectedSlot) return null;
              
              return (
                <div className="mt-2 text-sm text-green-700">
                  {formatDate(selectedSlot.date)} at {formatTime(selectedSlot.start_time)} - {formatTime(selectedSlot.end_time)}
                </div>
              );
            })()}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
