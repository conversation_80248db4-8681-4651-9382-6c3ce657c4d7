"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { DollarSign, Wrench, Truck, Clock } from "lucide-react";

interface PricingDisplayFieldProps {
  formData: Record<string, any>;
}

export function PricingDisplayField({ formData }: PricingDisplayFieldProps) {
  // Calculate pricing based on form data
  const getServicePrice = () => {
    const category = formData.service_category;
    const serviceType = formData.service_type;
    
    // Base pricing by category and type
    const basePrices: Record<string, Record<string, number>> = {
      plumbing: {
        repair: 150,
        install: 200,
        maintenance: 120,
        other: 150
      },
      'heating-cooling': {
        repair: 180,
        install: 250,
        maintenance: 140,
        other: 180
      },
      electrical: {
        repair: 160,
        install: 220,
        maintenance: 130,
        other: 160
      }
    };
    
    return basePrices[category]?.[serviceType] || 150;
  };

  const getDispatchFee = () => {
    // Emergency services have higher dispatch fee
    if (formData.emergency_type) {
      return 150;
    }
    return 75;
  };

  const servicePrice = getServicePrice();
  const dispatchFee = getDispatchFee();
  const totalEstimate = servicePrice + dispatchFee;

  return (
    <Card className="bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-800">
          <DollarSign className="w-5 h-5" />
          Service Pricing
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Service Details */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Wrench className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">Service Call</span>
            </div>
            <span className="font-medium">${servicePrice}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Truck className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">Dispatch Fee</span>
            </div>
            <span className="font-medium">${dispatchFee}</span>
          </div>
        </div>

        <Separator />

        {/* Total */}
        <div className="flex items-center justify-between text-lg font-bold">
          <span>Total Estimate</span>
          <span className="text-blue-600">${totalEstimate}</span>
        </div>

        {/* Pricing Notes */}
        <div className="space-y-2 text-xs text-muted-foreground">
          <div className="flex items-start gap-2">
            <Clock className="w-3 h-3 mt-0.5 flex-shrink-0" />
            <span>
              This is an estimate for the service call and dispatch. Additional work will be quoted on-site.
            </span>
          </div>
          
          <div className="flex flex-wrap gap-1 mt-2">
            <Badge variant="secondary" className="text-xs">
              No hidden fees
            </Badge>
            <Badge variant="secondary" className="text-xs">
              Upfront pricing
            </Badge>
            <Badge variant="secondary" className="text-xs">
              Licensed & insured
            </Badge>
          </div>
        </div>

        {/* Payment Info */}
        <div className="bg-white/50 rounded-lg p-3 text-xs">
          <p className="font-medium text-blue-800 mb-1">Payment Information:</p>
          <ul className="space-y-1 text-muted-foreground">
            <li>• Dispatch fee due upon technician arrival</li>
            <li>• Service fee applied to any additional work</li>
            <li>• We accept cash, check, and all major credit cards</li>
            <li>• Financing options available for larger projects</li>
          </ul>
        </div>

        {/* Guarantee */}
        <div className="text-center">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            ✓ 100% Satisfaction Guarantee
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
