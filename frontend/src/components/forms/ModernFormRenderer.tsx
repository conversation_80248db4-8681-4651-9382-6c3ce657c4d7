'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Wrench, ChevronLeft, ChevronRight } from 'lucide-react';
import { FormField } from './fields/FormField';
import { type FormTemplate, type FormPage, type FormField as FormFieldType } from '@/data/services';
import { cn } from '@/lib/utils';

interface ModernFormRendererProps {
  template: FormTemplate;
  onSubmit: (data: Record<string, any>) => void;
  onSave?: (data: Record<string, any>) => void;
  customProps?: Record<string, any>;
  className?: string;
}

// Progress Bar Component matching old design
const BookingProgress = ({ 
  currentStep, 
  totalSteps, 
  steps 
}: { 
  currentStep: number; 
  totalSteps: number; 
  steps: string[]; 
}) => {
  return (
    <div className="w-full py-4">
      <div className="flex items-center justify-between relative">
        {/* Progress line */}
        <div className="absolute top-6 left-0 right-0 h-0.5" style={{ backgroundColor: 'var(--color-booking-step-inactive)' }}>
          <div
            className="h-full transition-all duration-500 ease-out"
            style={{
              backgroundColor: 'var(--color-booking-step)',
              width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%`,
            }}
          />
        </div>

        {/* Steps */}
        {steps.map((step, index) => {
          const stepNumber = index + 1;
          const isComplete = stepNumber < currentStep;
          const isCurrent = stepNumber === currentStep;
          const isInactive = stepNumber > currentStep;

          return (
            <div
              key={index}
              className="flex items-center relative z-10"
            >
              <div
                className={cn(
                  "w-4 h-4 rounded-full transition-all duration-300",
                  {
                    "shadow-lg": isCurrent,
                  }
                )}
                style={{
                  backgroundColor: isCurrent 
                    ? 'var(--color-booking-step)' 
                    : isComplete 
                    ? 'var(--color-booking-step-complete)' 
                    : 'var(--color-booking-step-inactive)'
                }}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export const ModernFormRenderer: React.FC<ModernFormRendererProps> = ({ 
  template, 
  onSubmit, 
  onSave,
  customProps = {},
  className 
}) => {
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [isComplete, setIsComplete] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const currentPage = template.pages[currentPageIndex];
  const isLastPage = currentPageIndex === template.pages.length - 1;
  const isFirstPage = currentPageIndex === 0;
  const stepNames = template.pages.map(page => page.title);

  const updateFormData = (fieldId: string, value: any) => {
    setFormData((prev) => ({ ...prev, [fieldId]: value }));
    // Clear error when user starts typing
    if (errors[fieldId]) {
      setErrors(prev => ({ ...prev, [fieldId]: '' }));
    }
  };

  const validateCurrentPage = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    currentPage.fields.forEach(field => {
      if (field.required && (!formData[field.id] || formData[field.id] === '')) {
        newErrors[field.id] = `${field.label} is required`;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleNext = () => {
    if (!validateCurrentPage()) {
      return;
    }

    if (isLastPage) {
      handleSubmit();
    } else {
      setCurrentPageIndex(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (!isFirstPage) {
      setCurrentPageIndex(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    if (!validateCurrentPage()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      setIsComplete(true);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isComplete) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4" style={{ background: 'var(--gradient-subtle)' }}>
        <motion.div 
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="w-full max-w-md text-center"
        >
          <div 
            className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6"
            style={{ backgroundColor: 'var(--color-booking-step-complete)' }}
          >
            <CheckCircle className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Form Submitted!
          </h1>
          <p className="text-muted-foreground mb-8">
            Thank you for your submission. We'll get back to you shortly.
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div 
      className={cn("min-h-screen", className)}
      style={{ background: 'linear-gradient(to br, #ddd6fe, #ffffff, #dcfce7)' }}
    >
      <div className="container mx-auto px-4 py-6 overflow-y-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div 
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-6"
            style={{ background: 'var(--gradient-primary)' }}
          >
            <Wrench className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl sm:text-4xl font-bold mb-3" style={{ background: 'var(--gradient-primary)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
            {template.name}
          </h1>
          <p className="text-base sm:text-md text-muted-foreground max-w-2xl mx-auto">
            {template.description}
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="max-w-4xl mx-auto">
          <BookingProgress
            currentStep={currentPageIndex + 1}
            totalSteps={template.pages.length}
            steps={stepNames}
          />
        </div>

        {/* Form Steps */}
        <div className="max-w-4xl mx-auto">
          <Card 
            className="backdrop-blur-sm border border-white/20 shadow-2xl"
            style={{ background: 'rgba(255, 255, 255, 0.8)' }}
          >
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {currentPage.title}
              </CardTitle>
              {currentPage.description && (
                <p className="text-muted-foreground">{currentPage.description}</p>
              )}
            </CardHeader>
            <CardContent className="space-y-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentPageIndex}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-4"
                >
                  {currentPage.fields.map((field) => (
                    <div key={field.id}>
                      <FormField
                        field={field}
                        value={formData[field.id] || ''}
                        onChange={(value) => updateFormData(field.id, value)}
                        error={errors[field.id]}
                        {...customProps}
                      />
                    </div>
                  ))}
                </motion.div>
              </AnimatePresence>

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-6">
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={isFirstPage}
                  className="flex items-center gap-2"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Back
                </Button>

                <Button
                  onClick={handleNext}
                  disabled={isSubmitting}
                  className="flex items-center gap-2"
                  style={{ background: 'var(--gradient-primary)' }}
                >
                  {isLastPage ? 'Submit' : 'Continue'}
                  {!isLastPage && <ChevronRight className="w-4 h-4" />}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
