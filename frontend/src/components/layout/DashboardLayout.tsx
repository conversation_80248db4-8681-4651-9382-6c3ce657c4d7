'use client';

import * as React from 'react';
import { AppSidebar } from './AppSidebar';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { usePathname } from 'next/navigation';
import { ModernHeader } from './ModernHeader';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

// Helper function to generate breadcrumbs from pathname
function generateBreadcrumbs(pathname: string) {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs = [];

  // Add home
  breadcrumbs.push({
    label: 'Home',
    href: '/dashboard',
    isActive: pathname === '/dashboard',
  });

  // Build breadcrumbs from segments
  let currentPath = '';
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];
    currentPath += `/${segment}`;

    // Skip the first segment if it's 'dashboard'
    if (i === 0 && segment === 'dashboard') continue;

    const isLast = i === segments.length - 1;
    const label = segment
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    breadcrumbs.push({
      label,
      href: currentPath,
      isActive: isLast,
    });
  }

  return breadcrumbs;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname);

  return (
    <div className="min-h-screen bg-background">
      {/* Modern Header */}
      <ModernHeader />

      {/* Sidebar Layout */}
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          {/* Breadcrumb Navigation */}
          <div className="flex h-12 shrink-0 items-center gap-2 border-b px-4 bg-muted/30">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((breadcrumb, index) => (
                  <React.Fragment key={breadcrumb.href}>
                    <BreadcrumbItem className={index === 0 ? 'hidden md:block' : ''}>
                      {breadcrumb.isActive ? (
                        <BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink href={breadcrumb.href}>{breadcrumb.label}</BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                    {index < breadcrumbs.length - 1 && (
                      <BreadcrumbSeparator className="hidden md:block" />
                    )}
                  </React.Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>

          {/* Main content with modern styling */}
          <div
            className="flex flex-1 flex-col gap-6 p-6"
            style={{
              background:
                'linear-gradient(to br, rgba(139, 92, 246, 0.03), rgba(255, 255, 255, 0.8), rgba(34, 197, 94, 0.03))',
              minHeight: 'calc(100vh - 112px)', // Account for header + breadcrumb
            }}
          >
            {children}
          </div>
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}
