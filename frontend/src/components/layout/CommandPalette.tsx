'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from '@/components/ui/command';
import {
  Calculator,
  Calendar,
  CreditCard,
  Settings,
  Smile,
  User,
  Home,
  Users,
  FileText,
  MessageSquare,
  Bot,
  Building2,
  Zap,
  HelpCircle,
  Search,
  Plus,
  Phone,
  Mail,
  BarChart3,
  Clock,
  Star,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Copy,
  Share2,
  Eye,
  RefreshCw,
  LogOut,
} from 'lucide-react';

interface CommandPaletteProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export function CommandPalette({ open, setOpen }: CommandPaletteProps) {
  const router = useRouter();

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen(!open);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, [open, setOpen]);

  const runCommand = React.useCallback((command: () => void) => {
    setOpen(false);
    command();
  }, [setOpen]);

  return (
    <CommandDialog open={open} onOpenChange={setOpen}>
      <CommandInput placeholder="Type a command or search..." />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        
        <CommandGroup heading="Navigation">
          <CommandItem
            onSelect={() => runCommand(() => router.push('/dashboard'))}
          >
            <Home className="mr-2 h-4 w-4" />
            <span>Dashboard</span>
            <CommandShortcut>⌘D</CommandShortcut>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/jobs'))}
          >
            <Calendar className="mr-2 h-4 w-4" />
            <span>Jobs & Bookings</span>
            <CommandShortcut>⌘J</CommandShortcut>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/customers'))}
          >
            <Users className="mr-2 h-4 w-4" />
            <span>Customers</span>
            <CommandShortcut>⌘U</CommandShortcut>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/web-forms'))}
          >
            <FileText className="mr-2 h-4 w-4" />
            <span>Web Forms</span>
            <CommandShortcut>⌘F</CommandShortcut>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/agents'))}
          >
            <Bot className="mr-2 h-4 w-4" />
            <span>AI Agents</span>
            <CommandShortcut>⌘A</CommandShortcut>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/call-center'))}
          >
            <Phone className="mr-2 h-4 w-4" />
            <span>Call Center</span>
            <CommandShortcut>⌘C</CommandShortcut>
          </CommandItem>
        </CommandGroup>

        <CommandSeparator />

        <CommandGroup heading="Quick Actions">
          <CommandItem
            onSelect={() => runCommand(() => router.push('/jobs/new'))}
          >
            <Plus className="mr-2 h-4 w-4" />
            <span>Create New Job</span>
            <CommandShortcut>⌘N</CommandShortcut>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/customers/new'))}
          >
            <User className="mr-2 h-4 w-4" />
            <span>Add Customer</span>
            <CommandShortcut>⌘⇧N</CommandShortcut>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/web-forms/new'))}
          >
            <FileText className="mr-2 h-4 w-4" />
            <span>Create Form</span>
            <CommandShortcut>⌘⇧F</CommandShortcut>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/agents/new'))}
          >
            <Bot className="mr-2 h-4 w-4" />
            <span>Create Agent</span>
            <CommandShortcut>⌘⇧A</CommandShortcut>
          </CommandItem>
        </CommandGroup>

        <CommandSeparator />

        <CommandGroup heading="Analytics & Reports">
          <CommandItem
            onSelect={() => runCommand(() => router.push('/dashboard/analytics'))}
          >
            <BarChart3 className="mr-2 h-4 w-4" />
            <span>Analytics Dashboard</span>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/dashboard/reports'))}
          >
            <FileText className="mr-2 h-4 w-4" />
            <span>Generate Report</span>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/customers/reviews'))}
          >
            <Star className="mr-2 h-4 w-4" />
            <span>Customer Reviews</span>
          </CommandItem>
        </CommandGroup>

        <CommandSeparator />

        <CommandGroup heading="Communication">
          <CommandItem
            onSelect={() => runCommand(() => router.push('/messages'))}
          >
            <MessageSquare className="mr-2 h-4 w-4" />
            <span>Messages</span>
            <CommandShortcut>⌘M</CommandShortcut>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/email-campaigns'))}
          >
            <Mail className="mr-2 h-4 w-4" />
            <span>Email Campaigns</span>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/notifications'))}
          >
            <Clock className="mr-2 h-4 w-4" />
            <span>Notifications</span>
          </CommandItem>
        </CommandGroup>

        <CommandSeparator />

        <CommandGroup heading="Settings">
          <CommandItem
            onSelect={() => runCommand(() => router.push('/settings/company'))}
          >
            <Building2 className="mr-2 h-4 w-4" />
            <span>Company Settings</span>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/integrations'))}
          >
            <Zap className="mr-2 h-4 w-4" />
            <span>Integrations</span>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/billing'))}
          >
            <CreditCard className="mr-2 h-4 w-4" />
            <span>Billing</span>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => router.push('/settings'))}
          >
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
            <CommandShortcut>⌘,</CommandShortcut>
          </CommandItem>
        </CommandGroup>

        <CommandSeparator />

        <CommandGroup heading="Help & Support">
          <CommandItem
            onSelect={() => runCommand(() => router.push('/support'))}
          >
            <HelpCircle className="mr-2 h-4 w-4" />
            <span>Help & Support</span>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => window.open('https://docs.homeservices.com', '_blank'))}
          >
            <FileText className="mr-2 h-4 w-4" />
            <span>Documentation</span>
          </CommandItem>
        </CommandGroup>

        <CommandSeparator />

        <CommandGroup heading="Tools">
          <CommandItem>
            <Calculator className="mr-2 h-4 w-4" />
            <span>Calculator</span>
          </CommandItem>
          <CommandItem>
            <Smile className="mr-2 h-4 w-4" />
            <span>Search Emoji</span>
          </CommandItem>
          <CommandItem
            onSelect={() => runCommand(() => {
              // Refresh the current page
              window.location.reload();
            })}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            <span>Refresh Page</span>
            <CommandShortcut>⌘R</CommandShortcut>
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  );
}
