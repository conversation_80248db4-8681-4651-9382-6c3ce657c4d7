'use client';

import * as React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {
  Calendar,
  ChevronUp,
  Home,
  Settings,
  User2,
  Users,
  FileText,
  MessageSquare,
  Wrench,
  Building2,
  CreditCard,
  HelpCircle,
  LogOut,
  Zap,
  Bot,
} from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useCompany } from '@/contexts/CompanyContext';

// Navigation data for home services platform
const data = {
  user: {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/john-doe.jpg',
  },
  navMain: [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: Home,
      isActive: true,
      items: [
        {
          title: 'Overview',
          url: '/dashboard',
        },
        {
          title: 'Analytics',
          url: '/dashboard/analytics',
        },
        {
          title: 'Reports',
          url: '/dashboard/reports',
        },
      ],
    },
    {
      title: 'Bookings & Jobs',
      url: '/jobs',
      icon: Calendar,
      items: [
        {
          title: 'All Jobs',
          url: '/jobs',
        },
        {
          title: 'Scheduled',
          url: '/jobs/scheduled',
        },
        {
          title: 'In Progress',
          url: '/jobs/in-progress',
        },
        {
          title: 'Completed',
          url: '/jobs/completed',
        },
        {
          title: 'Cancelled',
          url: '/jobs/cancelled',
        },
      ],
    },
    {
      title: 'Customers',
      url: '/customers',
      icon: Users,
      items: [
        {
          title: 'All Customers',
          url: '/customers',
        },
        {
          title: 'Active',
          url: '/customers/active',
        },
        {
          title: 'Leads',
          url: '/customers/leads',
        },
        {
          title: 'Reviews',
          url: '/customers/reviews',
        },
      ],
    },
    {
      title: 'Communication',
      url: '/communication',
      icon: MessageSquare,
      items: [
        {
          title: 'Call Center',
          url: '/call-center',
        },
        {
          title: 'Messages',
          url: '/messages',
        },
        {
          title: 'Email Campaigns',
          url: '/email-campaigns',
        },
        {
          title: 'Notifications',
          url: '/notifications',
        },
      ],
    },
    {
      title: 'Web Forms',
      url: '/web-forms',
      icon: FileText,
      items: [
        {
          title: 'All Forms',
          url: '/web-forms',
        },
        {
          title: 'Templates',
          url: '/web-forms/templates',
        },
        {
          title: 'Submissions',
          url: '/web-forms/submissions',
        },
        {
          title: 'Analytics',
          url: '/web-forms/analytics',
        },
      ],
    },
    {
      title: 'AI Agents',
      url: '/agents',
      icon: Bot,
      items: [
        {
          title: 'All Agents',
          url: '/agents',
        },
        {
          title: 'Voice Agents',
          url: '/agents/voice',
        },
        {
          title: 'Chat Agents',
          url: '/agents/chat',
        },
        {
          title: 'Templates',
          url: '/agents/templates',
        },
        {
          title: 'Training',
          url: '/agents/training',
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: 'Company Settings',
      url: '/settings/company',
      icon: Building2,
    },
    {
      title: 'Billing',
      url: '/billing',
      icon: CreditCard,
    },
    {
      title: 'Integrations',
      url: '/integrations',
      icon: Zap,
    },
    {
      title: 'Help & Support',
      url: '/support',
      icon: HelpCircle,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const router = useRouter();
  const pathname = usePathname();
  const { currentCompany } = useCompany();

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                    <Wrench className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {currentCompany?.name || 'Home Services'}
                    </span>
                    <span className="truncate text-xs">
                      {currentCompany?.industry || 'Professional Services'}
                    </span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="start"
                sideOffset={4}
              >
                <DropdownMenuItem onClick={() => router.push('/settings/company')}>
                  <Building2 className="mr-2 size-4" />
                  Company Settings
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push('/billing')}>
                  <CreditCard className="mr-2 size-4" />
                  Billing
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push('/integrations')}>
                  <Zap className="mr-2 size-4" />
                  Integrations
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {data.navMain.map((item) => (
          <SidebarGroup key={item.title}>
            <SidebarGroupLabel>{item.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {item.items?.map((subItem) => (
                  <SidebarMenuItem key={subItem.title}>
                    <SidebarMenuButton asChild isActive={pathname === subItem.url}>
                      <a href={subItem.url}>
                        <item.icon className="size-4" />
                        <span>{subItem.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}

        <SidebarGroup className="mt-auto">
          <SidebarGroupContent>
            <SidebarMenu>
              {data.navSecondary.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild size="sm">
                    <a href={item.url}>
                      <item.icon className="size-4" />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={data.user.avatar} alt={data.user.name} />
                    <AvatarFallback className="rounded-lg">
                      {data.user.name
                        .split(' ')
                        .map((n) => n[0])
                        .join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{data.user.name}</span>
                    <span className="truncate text-xs">{data.user.email}</span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="top"
                align="start"
                sideOffset={4}
              >
                <DropdownMenuItem onClick={() => router.push('/profile')}>
                  <User2 className="mr-2 size-4" />
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push('/settings')}>
                  <Settings className="mr-2 size-4" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push('/logout')}>
                  <LogOut className="mr-2 size-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
