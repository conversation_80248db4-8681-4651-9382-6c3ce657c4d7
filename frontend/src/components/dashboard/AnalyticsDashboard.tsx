'use client';

import * as React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Calendar,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Activity,
  Clock,
  Phone,
  MessageSquare,
  Star,
  MoreHorizontal,
  Download,
  Filter,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Target,
  CheckCircle,
  AlertCircle,
  XCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Mock data for demonstration
const mockData = {
  overview: {
    totalRevenue: 45231.89,
    revenueChange: 20.1,
    totalJobs: 2350,
    jobsChange: 180.1,
    activeCustomers: 12234,
    customersChange: 19,
    conversionRate: 3.2,
    conversionChange: -2.1,
  },
  recentJobs: [
    {
      id: 'JOB-001',
      customer: 'John Smith',
      service: 'Plumbing Repair',
      status: 'completed',
      amount: 285.00,
      date: '2024-01-15',
      technician: 'Mike Johnson',
    },
    {
      id: 'JOB-002',
      customer: 'Sarah Wilson',
      service: 'HVAC Maintenance',
      status: 'in-progress',
      amount: 450.00,
      date: '2024-01-15',
      technician: 'David Brown',
    },
    {
      id: 'JOB-003',
      customer: 'Robert Davis',
      service: 'Electrical Installation',
      status: 'scheduled',
      amount: 750.00,
      date: '2024-01-16',
      technician: 'Lisa Garcia',
    },
    {
      id: 'JOB-004',
      customer: 'Emily Johnson',
      service: 'Roofing Inspection',
      status: 'completed',
      amount: 195.00,
      date: '2024-01-14',
      technician: 'Tom Wilson',
    },
  ],
  topServices: [
    { name: 'Plumbing', jobs: 145, revenue: 28500, growth: 12.5 },
    { name: 'HVAC', jobs: 98, revenue: 45200, growth: 8.3 },
    { name: 'Electrical', jobs: 87, revenue: 32100, growth: 15.7 },
    { name: 'Roofing', jobs: 65, revenue: 18900, growth: -2.1 },
    { name: 'Landscaping', jobs: 52, revenue: 12400, growth: 22.1 },
  ],
  monthlyStats: [
    { month: 'Jan', jobs: 186, revenue: 12400 },
    { month: 'Feb', jobs: 205, revenue: 14200 },
    { month: 'Mar', jobs: 237, revenue: 16800 },
    { month: 'Apr', jobs: 273, revenue: 19200 },
    { month: 'May', jobs: 209, revenue: 15600 },
    { month: 'Jun', jobs: 214, revenue: 16400 },
  ],
};

function StatCard({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  format = 'number' 
}: {
  title: string;
  value: number;
  change: number;
  icon: React.ElementType;
  format?: 'number' | 'currency' | 'percentage';
}) {
  const formatValue = (val: number) => {
    switch (format) {
      case 'currency':
        return `$${val.toLocaleString()}`;
      case 'percentage':
        return `${val}%`;
      default:
        return val.toLocaleString();
    }
  };

  const isPositive = change > 0;

  return (
    <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary/20">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
          <Icon className="h-4 w-4 text-primary" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue(value)}</div>
        <div className="flex items-center text-xs text-muted-foreground mt-1">
          {isPositive ? (
            <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
          ) : (
            <ArrowDownRight className="h-3 w-3 text-red-500 mr-1" />
          )}
          <span className={isPositive ? 'text-green-600' : 'text-red-600'}>
            {Math.abs(change)}%
          </span>
          <span className="ml-1">from last month</span>
        </div>
      </CardContent>
    </Card>
  );
}

function JobStatusBadge({ status }: { status: string }) {
  const variants = {
    completed: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
    'in-progress': { variant: 'secondary' as const, icon: Activity, color: 'text-blue-600' },
    scheduled: { variant: 'outline' as const, icon: Clock, color: 'text-orange-600' },
    cancelled: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
  };

  const config = variants[status as keyof typeof variants] || variants.scheduled;
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className={cn('h-3 w-3', config.color)} />
      {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
    </Badge>
  );
}

export function AnalyticsDashboard() {
  const [timeRange, setTimeRange] = React.useState('30d');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Track your business performance and key metrics
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 3 months</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Revenue"
          value={mockData.overview.totalRevenue}
          change={mockData.overview.revenueChange}
          icon={DollarSign}
          format="currency"
        />
        <StatCard
          title="Total Jobs"
          value={mockData.overview.totalJobs}
          change={mockData.overview.jobsChange}
          icon={Zap}
        />
        <StatCard
          title="Active Customers"
          value={mockData.overview.activeCustomers}
          change={mockData.overview.customersChange}
          icon={Users}
        />
        <StatCard
          title="Conversion Rate"
          value={mockData.overview.conversionRate}
          change={mockData.overview.conversionChange}
          icon={Target}
          format="percentage"
        />
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="jobs">Recent Jobs</TabsTrigger>
          <TabsTrigger value="services">Top Services</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Revenue Overview</CardTitle>
                <CardDescription>
                  Monthly revenue and job completion trends
                </CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                  Chart Component Would Go Here
                  <br />
                  (Revenue: ${mockData.overview.totalRevenue.toLocaleString()})
                </div>
              </CardContent>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Service Distribution</CardTitle>
                <CardDescription>
                  Breakdown by service type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockData.topServices.slice(0, 3).map((service) => (
                    <div key={service.name} className="flex items-center">
                      <div className="w-full">
                        <div className="flex items-center justify-between text-sm">
                          <span className="font-medium">{service.name}</span>
                          <span className="text-muted-foreground">
                            {service.jobs} jobs
                          </span>
                        </div>
                        <Progress 
                          value={(service.jobs / 145) * 100} 
                          className="mt-2" 
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="jobs" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Jobs</CardTitle>
                <CardDescription>
                  Latest job completions and updates
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockData.recentJobs.map((job) => (
                  <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="text-sm font-medium">{job.customer}</p>
                        <p className="text-xs text-muted-foreground">{job.service}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <JobStatusBadge status={job.status} />
                      <div className="text-right">
                        <p className="text-sm font-medium">${job.amount}</p>
                        <p className="text-xs text-muted-foreground">{job.date}</p>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>Edit Job</DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>Contact Customer</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {mockData.topServices.map((service) => (
              <Card key={service.name} className="hover:shadow-lg transition-all duration-300">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {service.name}
                  </CardTitle>
                  <Badge variant={service.growth > 0 ? 'default' : 'secondary'}>
                    {service.growth > 0 ? '+' : ''}{service.growth}%
                  </Badge>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{service.jobs}</div>
                  <p className="text-xs text-muted-foreground">
                    ${service.revenue.toLocaleString()} revenue
                  </p>
                  <div className="mt-4">
                    <Progress value={(service.jobs / 145) * 100} />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
              <CardDescription>
                Monthly performance metrics and growth patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center text-muted-foreground">
                Advanced Chart Component Would Go Here
                <br />
                (Showing trends for {mockData.monthlyStats.length} months)
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
