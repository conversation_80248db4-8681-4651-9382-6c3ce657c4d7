'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  Calendar, 
  Phone, 
  MessageSquare, 
  Clock,
  Target,
  Zap,
  Star,
  CheckCircle,
  AlertCircle,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

interface StatCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    period: string;
    trend: 'up' | 'down';
  };
  icon: React.ElementType;
  description?: string;
  color?: string;
  progress?: number;
  target?: number;
  format?: 'currency' | 'percentage' | 'number';
  className?: string;
}

interface QuickStat {
  label: string;
  value: string;
  icon: React.ElementType;
  color: string;
}

const formatValue = (value: string | number, format?: string) => {
  if (typeof value === 'string') return value;
  
  switch (format) {
    case 'currency':
      return `$${value.toLocaleString()}`;
    case 'percentage':
      return `${value}%`;
    default:
      return value.toLocaleString();
  }
};

export function StatCard({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  description, 
  color = 'text-primary',
  progress,
  target,
  format,
  className 
}: StatCardProps) {
  const isPositive = change?.trend === 'up';
  const formattedValue = formatValue(value, format);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -2 }}
    >
      <Card className={cn(
        "relative overflow-hidden transition-all duration-300 hover:shadow-lg border-l-4",
        className
      )} style={{ borderLeftColor: color.replace('text-', '') === color ? '#8b5cf6' : undefined }}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className={cn("h-8 w-8 rounded-full flex items-center justify-center", 
              color.includes('text-') ? `bg-${color.replace('text-', '')}-100` : 'bg-primary/10'
            )}>
              <Icon className={cn("h-4 w-4", color)} />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6">
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>View Details</DropdownMenuItem>
                <DropdownMenuItem>Export Data</DropdownMenuItem>
                <DropdownMenuItem>Set Alert</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold mb-1">{formattedValue}</div>
          
          {change && (
            <div className="flex items-center text-xs text-muted-foreground">
              {isPositive ? (
                <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="h-3 w-3 text-red-500 mr-1" />
              )}
              <span className={isPositive ? 'text-green-600' : 'text-red-600'}>
                {Math.abs(change.value)}%
              </span>
              <span className="ml-1">from {change.period}</span>
            </div>
          )}
          
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
          
          {progress !== undefined && (
            <div className="mt-3 space-y-1">
              <div className="flex justify-between text-xs">
                <span>Progress</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-1" />
              {target && (
                <div className="text-xs text-muted-foreground">
                  Target: {formatValue(target, format)}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function QuickStatsBar({ stats }: { stats: QuickStat[] }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6"
    >
      {stats.map((stat, index) => (
        <motion.div
          key={stat.label}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
          className="flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm border"
        >
          <div className={cn("p-2 rounded-full", stat.color)}>
            <stat.icon className="h-4 w-4 text-white" />
          </div>
          <div>
            <div className="text-sm font-medium">{stat.value}</div>
            <div className="text-xs text-muted-foreground">{stat.label}</div>
          </div>
        </motion.div>
      ))}
    </motion.div>
  );
}

export function ModernStatsGrid() {
  const stats = [
    {
      title: 'Total Revenue',
      value: 45231.89,
      change: { value: 20.1, period: 'last month', trend: 'up' as const },
      icon: DollarSign,
      description: 'Monthly recurring revenue',
      color: 'text-green-600',
      format: 'currency' as const,
      progress: 75,
      target: 60000,
    },
    {
      title: 'Active Customers',
      value: 2350,
      change: { value: 12.5, period: 'last month', trend: 'up' as const },
      icon: Users,
      description: 'Customers with active bookings',
      color: 'text-blue-600',
      progress: 85,
      target: 3000,
    },
    {
      title: 'Jobs Completed',
      value: 1247,
      change: { value: 8.2, period: 'last month', trend: 'up' as const },
      icon: CheckCircle,
      description: 'Successfully completed jobs',
      color: 'text-purple-600',
      progress: 92,
      target: 1500,
    },
    {
      title: 'Conversion Rate',
      value: 3.2,
      change: { value: 2.1, period: 'last month', trend: 'down' as const },
      icon: Target,
      description: 'Leads to customers',
      color: 'text-orange-600',
      format: 'percentage' as const,
      progress: 64,
      target: 5,
    },
    {
      title: 'Average Response Time',
      value: '2.3 min',
      change: { value: 15.3, period: 'last week', trend: 'up' as const },
      icon: Clock,
      description: 'AI agent response time',
      color: 'text-cyan-600',
    },
    {
      title: 'Customer Satisfaction',
      value: 4.8,
      change: { value: 5.2, period: 'last month', trend: 'up' as const },
      icon: Star,
      description: 'Average rating',
      color: 'text-yellow-600',
      progress: 96,
      target: 5,
    },
    {
      title: 'Calls Handled',
      value: 892,
      change: { value: 23.1, period: 'last week', trend: 'up' as const },
      icon: Phone,
      description: 'AI agent calls this month',
      color: 'text-indigo-600',
    },
    {
      title: 'Messages Sent',
      value: 3421,
      change: { value: 18.7, period: 'last month', trend: 'up' as const },
      icon: MessageSquare,
      description: 'Automated messages',
      color: 'text-pink-600',
    },
  ];

  const quickStats = [
    { label: 'Online Now', value: '24', icon: Zap, color: 'bg-green-500' },
    { label: 'Pending', value: '12', icon: Clock, color: 'bg-yellow-500' },
    { label: 'Urgent', value: '3', icon: AlertCircle, color: 'bg-red-500' },
    { label: 'Scheduled', value: '45', icon: Calendar, color: 'bg-blue-500' },
  ];

  return (
    <div className="space-y-6">
      <QuickStatsBar stats={quickStats} />
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <StatCard
            key={stat.title}
            {...stat}
            className="hover:shadow-xl transition-all duration-300"
          />
        ))}
      </div>
    </div>
  );
}

// Real-time stats component
export function RealTimeStats() {
  const [stats, setStats] = React.useState({
    activeUsers: 24,
    callsInProgress: 8,
    messagesPerMinute: 12,
    systemLoad: 45,
  });

  React.useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        activeUsers: prev.activeUsers + Math.floor(Math.random() * 3) - 1,
        callsInProgress: Math.max(0, prev.callsInProgress + Math.floor(Math.random() * 3) - 1),
        messagesPerMinute: prev.messagesPerMinute + Math.floor(Math.random() * 5) - 2,
        systemLoad: Math.max(0, Math.min(100, prev.systemLoad + Math.floor(Math.random() * 10) - 5)),
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="grid grid-cols-2 lg:grid-cols-4 gap-4"
    >
      <Card className="p-4 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-green-600">Active Users</p>
            <p className="text-2xl font-bold text-green-700">{stats.activeUsers}</p>
          </div>
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </div>
      </Card>

      <Card className="p-4 bg-gradient-to-br from-blue-50 to-cyan-50 border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-blue-600">Calls Active</p>
            <p className="text-2xl font-bold text-blue-700">{stats.callsInProgress}</p>
          </div>
          <Phone className="w-5 h-5 text-blue-500" />
        </div>
      </Card>

      <Card className="p-4 bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-purple-600">Messages/min</p>
            <p className="text-2xl font-bold text-purple-700">{stats.messagesPerMinute}</p>
          </div>
          <MessageSquare className="w-5 h-5 text-purple-500" />
        </div>
      </Card>

      <Card className="p-4 bg-gradient-to-br from-orange-50 to-red-50 border-orange-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-orange-600">System Load</p>
            <p className="text-2xl font-bold text-orange-700">{stats.systemLoad}%</p>
          </div>
          <div className="w-8 h-8">
            <Progress value={stats.systemLoad} className="h-2" />
          </div>
        </div>
      </Card>
    </motion.div>
  );
}
