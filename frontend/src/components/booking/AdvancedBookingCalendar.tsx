'use client';

import * as React from 'react';
import { addDays, format, isSameDay, isToday, startOfWeek, endOfWeek } from 'date-fns';
import { Calendar as CalendarIcon, Clock, User, MapPin, Phone, Plus, Filter } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

interface BookingSlot {
  id: string;
  date: Date;
  startTime: string;
  endTime: string;
  customer: {
    name: string;
    phone: string;
    avatar?: string;
  };
  service: {
    type: string;
    category: 'plumbing' | 'hvac' | 'electrical' | 'roofing' | 'landscaping';
    duration: number;
  };
  technician: {
    name: string;
    avatar?: string;
  };
  status: 'confirmed' | 'pending' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  address: string;
  notes?: string;
}

// Mock booking data
const mockBookings: BookingSlot[] = [
  {
    id: '1',
    date: new Date(),
    startTime: '09:00',
    endTime: '11:00',
    customer: {
      name: 'John Smith',
      phone: '+****************',
      avatar: '/avatars/john.jpg',
    },
    service: {
      type: 'Kitchen Sink Repair',
      category: 'plumbing',
      duration: 120,
    },
    technician: {
      name: 'Mike Johnson',
      avatar: '/avatars/mike.jpg',
    },
    status: 'confirmed',
    priority: 'medium',
    address: '123 Main St, Anytown, ST 12345',
    notes: 'Customer prefers morning appointments',
  },
  {
    id: '2',
    date: addDays(new Date(), 1),
    startTime: '14:00',
    endTime: '16:30',
    customer: {
      name: 'Sarah Wilson',
      phone: '+****************',
    },
    service: {
      type: 'HVAC Maintenance',
      category: 'hvac',
      duration: 150,
    },
    technician: {
      name: 'David Brown',
    },
    status: 'pending',
    priority: 'high',
    address: '456 Oak Ave, Somewhere, ST 67890',
  },
  {
    id: '3',
    date: addDays(new Date(), 2),
    startTime: '10:30',
    endTime: '12:00',
    customer: {
      name: 'Robert Davis',
      phone: '+****************',
    },
    service: {
      type: 'Electrical Installation',
      category: 'electrical',
      duration: 90,
    },
    technician: {
      name: 'Lisa Garcia',
    },
    status: 'confirmed',
    priority: 'low',
    address: '789 Pine Rd, Elsewhere, ST 13579',
  },
];

const serviceColors = {
  plumbing: 'bg-blue-100 text-blue-800 border-blue-200',
  hvac: 'bg-purple-100 text-purple-800 border-purple-200',
  electrical: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  roofing: 'bg-orange-100 text-orange-800 border-orange-200',
  landscaping: 'bg-green-100 text-green-800 border-green-200',
};

const statusColors = {
  confirmed: 'bg-green-100 text-green-800',
  pending: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-gray-100 text-gray-800',
  cancelled: 'bg-red-100 text-red-800',
};

const priorityColors = {
  low: 'bg-gray-100 text-gray-600',
  medium: 'bg-blue-100 text-blue-600',
  high: 'bg-orange-100 text-orange-600',
  urgent: 'bg-red-100 text-red-600',
};

function BookingCard({ booking }: { booking: BookingSlot }) {
  return (
    <Card className="mb-3 hover:shadow-md transition-shadow cursor-pointer">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={booking.customer.avatar} alt={booking.customer.name} />
              <AvatarFallback>
                {booking.customer.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <h4 className="font-medium text-sm">{booking.customer.name}</h4>
              <p className="text-xs text-muted-foreground">{booking.service.type}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={cn('text-xs', statusColors[booking.status])}>
              {booking.status}
            </Badge>
            <Badge className={cn('text-xs', priorityColors[booking.priority])}>
              {booking.priority}
            </Badge>
          </div>
        </div>
        
        <div className="space-y-2 text-xs text-muted-foreground">
          <div className="flex items-center gap-2">
            <Clock className="h-3 w-3" />
            <span>{booking.startTime} - {booking.endTime}</span>
          </div>
          <div className="flex items-center gap-2">
            <User className="h-3 w-3" />
            <span>{booking.technician.name}</span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="h-3 w-3" />
            <span className="truncate">{booking.address}</span>
          </div>
        </div>
        
        <div className="flex items-center justify-between mt-3 pt-3 border-t">
          <Badge className={cn('text-xs', serviceColors[booking.service.category])}>
            {booking.service.category}
          </Badge>
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" className="h-6 px-2">
              <Phone className="h-3 w-3" />
            </Button>
            <Button variant="ghost" size="sm" className="h-6 px-2">
              Edit
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function AdvancedBookingCalendar() {
  const [selectedDate, setSelectedDate] = React.useState<Date>(new Date());
  const [viewMode, setViewMode] = React.useState<'calendar' | 'list' | 'timeline'>('calendar');
  const [filterTechnician, setFilterTechnician] = React.useState<string>('all');
  const [filterService, setFilterService] = React.useState<string>('all');

  const selectedDateBookings = mockBookings.filter(booking =>
    isSameDay(booking.date, selectedDate)
  );

  const weekBookings = mockBookings.filter(booking => {
    const weekStart = startOfWeek(selectedDate);
    const weekEnd = endOfWeek(selectedDate);
    return booking.date >= weekStart && booking.date <= weekEnd;
  });

  const hasBookingsOnDate = (date: Date) => {
    return mockBookings.some(booking => isSameDay(booking.date, date));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Booking Calendar</h1>
          <p className="text-muted-foreground">
            Manage appointments and technician schedules
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={filterTechnician} onValueChange={setFilterTechnician}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="All Technicians" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Technicians</SelectItem>
              <SelectItem value="mike">Mike Johnson</SelectItem>
              <SelectItem value="david">David Brown</SelectItem>
              <SelectItem value="lisa">Lisa Garcia</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={filterService} onValueChange={setFilterService}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="All Services" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Services</SelectItem>
              <SelectItem value="plumbing">Plumbing</SelectItem>
              <SelectItem value="hvac">HVAC</SelectItem>
              <SelectItem value="electrical">Electrical</SelectItem>
              <SelectItem value="roofing">Roofing</SelectItem>
            </SelectContent>
          </Select>
          
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Booking
          </Button>
        </div>
      </div>

      {/* View Mode Tabs */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
        <TabsList>
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="list">List View</TabsTrigger>
          <TabsTrigger value="timeline">Timeline View</TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-3">
            {/* Calendar */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Schedule</CardTitle>
                <CardDescription>
                  Select a date to view appointments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => date && setSelectedDate(date)}
                  className="rounded-md border"
                  modifiers={{
                    hasBookings: (date) => hasBookingsOnDate(date),
                    today: (date) => isToday(date),
                  }}
                  modifiersStyles={{
                    hasBookings: {
                      backgroundColor: 'hsl(var(--primary))',
                      color: 'hsl(var(--primary-foreground))',
                      fontWeight: 'bold',
                    },
                  }}
                />
              </CardContent>
            </Card>

            {/* Daily Schedule */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4" />
                  {format(selectedDate, 'MMMM d, yyyy')}
                </CardTitle>
                <CardDescription>
                  {selectedDateBookings.length} appointment{selectedDateBookings.length !== 1 ? 's' : ''}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  {selectedDateBookings.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <CalendarIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No appointments scheduled</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {selectedDateBookings
                        .sort((a, b) => a.startTime.localeCompare(b.startTime))
                        .map((booking) => (
                          <BookingCard key={booking.id} booking={booking} />
                        ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Appointments</CardTitle>
              <CardDescription>
                Complete list of upcoming appointments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockBookings.map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-sm font-medium">
                          {format(booking.date, 'MMM')}
                        </div>
                        <div className="text-2xl font-bold">
                          {format(booking.date, 'd')}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium">{booking.customer.name}</h4>
                        <p className="text-sm text-muted-foreground">{booking.service.type}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Clock className="h-3 w-3" />
                          <span className="text-xs">{booking.startTime} - {booking.endTime}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={cn('text-xs', statusColors[booking.status])}>
                        {booking.status}
                      </Badge>
                      <Badge className={cn('text-xs', serviceColors[booking.service.category])}>
                        {booking.service.category}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Week Timeline</CardTitle>
              <CardDescription>
                Weekly view of all appointments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Array.from({ length: 7 }, (_, i) => {
                  const date = addDays(startOfWeek(selectedDate), i);
                  const dayBookings = weekBookings.filter(booking =>
                    isSameDay(booking.date, date)
                  );
                  
                  return (
                    <div key={i} className="border-l-2 border-muted pl-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="text-sm font-medium min-w-[100px]">
                          {format(date, 'EEE, MMM d')}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {dayBookings.length} appointment{dayBookings.length !== 1 ? 's' : ''}
                        </div>
                      </div>
                      <div className="space-y-2">
                        {dayBookings.map((booking) => (
                          <div key={booking.id} className="flex items-center gap-3 p-2 bg-muted/50 rounded">
                            <div className="text-xs font-mono">
                              {booking.startTime}
                            </div>
                            <div className="flex-1">
                              <div className="text-sm font-medium">{booking.customer.name}</div>
                              <div className="text-xs text-muted-foreground">{booking.service.type}</div>
                            </div>
                            <Badge className={cn('text-xs', serviceColors[booking.service.category])}>
                              {booking.service.category}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
