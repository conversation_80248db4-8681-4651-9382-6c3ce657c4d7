'use client';

import * as React from 'react';
import {
  Bar,
  BarChart,
  Line,
  LineChart,
  Area,
  AreaChart,
  Pie,
  PieChart,
  Cell,
  CartesianGrid,
  XAxis,
  YAxis,
  ResponsiveContainer,
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  DollarSign, 
  Users, 
  Phone,
  MessageSquare,
  Clock,
  Star,
  Download,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Mock data for charts
const revenueData = [
  { month: 'Jan', revenue: 12400, jobs: 186, customers: 80 },
  { month: 'Feb', revenue: 14200, jobs: 205, customers: 95 },
  { month: 'Mar', revenue: 16800, jobs: 237, customers: 120 },
  { month: 'Apr', revenue: 19200, jobs: 273, customers: 140 },
  { month: 'May', revenue: 15600, jobs: 209, customers: 110 },
  { month: 'Jun', revenue: 18400, jobs: 234, customers: 135 },
  { month: 'Jul', revenue: 21200, jobs: 298, customers: 165 },
  { month: 'Aug', revenue: 23800, jobs: 321, customers: 180 },
  { month: 'Sep', revenue: 20600, jobs: 287, customers: 155 },
  { month: 'Oct', revenue: 25400, jobs: 342, customers: 195 },
  { month: 'Nov', revenue: 28200, jobs: 378, customers: 220 },
  { month: 'Dec', revenue: 31600, jobs: 412, customers: 245 },
];

const serviceData = [
  { name: 'Plumbing', value: 35, revenue: 28500, color: '#8b5cf6' },
  { name: 'HVAC', value: 25, revenue: 45200, color: '#3b82f6' },
  { name: 'Electrical', value: 20, revenue: 32100, color: '#10b981' },
  { name: 'Roofing', value: 12, revenue: 18900, color: '#f59e0b' },
  { name: 'Landscaping', value: 8, revenue: 12400, color: '#ef4444' },
];

const performanceData = [
  { time: '00:00', calls: 12, messages: 45, bookings: 8 },
  { time: '04:00', calls: 8, messages: 32, bookings: 5 },
  { time: '08:00', calls: 24, messages: 78, bookings: 18 },
  { time: '12:00', calls: 32, messages: 95, bookings: 25 },
  { time: '16:00', calls: 28, messages: 82, bookings: 22 },
  { time: '20:00', calls: 18, messages: 56, bookings: 14 },
];

const chartConfig = {
  revenue: {
    label: 'Revenue',
    color: '#8b5cf6',
  },
  jobs: {
    label: 'Jobs',
    color: '#3b82f6',
  },
  customers: {
    label: 'Customers',
    color: '#10b981',
  },
  calls: {
    label: 'Calls',
    color: '#f59e0b',
  },
  messages: {
    label: 'Messages',
    color: '#ef4444',
  },
  bookings: {
    label: 'Bookings',
    color: '#8b5cf6',
  },
} satisfies ChartConfig;

export function RevenueChart() {
  const [timeRange, setTimeRange] = React.useState('12m');
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Revenue Overview</CardTitle>
          <CardDescription>Monthly revenue and growth trends</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3m">3 months</SelectItem>
              <SelectItem value="6m">6 months</SelectItem>
              <SelectItem value="12m">12 months</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[400px]">
          <AreaChart data={revenueData}>
            <defs>
              <linearGradient id="fillRevenue" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="var(--color-revenue)" stopOpacity={0.8} />
                <stop offset="95%" stopColor="var(--color-revenue)" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => `$${value.toLocaleString()}`}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  className="w-[200px]"
                  nameKey="revenue"
                  labelFormatter={(value) => `Month: ${value}`}
                />
              }
            />
            <Area
              dataKey="revenue"
              type="monotone"
              fill="url(#fillRevenue)"
              fillOpacity={0.6}
              stroke="var(--color-revenue)"
              strokeWidth={2}
            />
          </AreaChart>
        </ChartContainer>
        <div className="flex items-center gap-2 text-sm text-muted-foreground mt-4">
          <TrendingUp className="h-4 w-4 text-green-500" />
          <span>+12.5% from last period</span>
        </div>
      </CardContent>
    </Card>
  );
}

export function ServiceDistributionChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Service Distribution</CardTitle>
        <CardDescription>Breakdown of services by volume and revenue</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px]">
          <PieChart>
            <ChartTooltip
              content={
                <ChartTooltipContent
                  nameKey="name"
                  formatter={(value, name) => [
                    `${value}%`,
                    name,
                  ]}
                />
              }
            />
            <Pie
              data={serviceData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {serviceData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
          </PieChart>
        </ChartContainer>
        <div className="grid grid-cols-2 gap-4 mt-4">
          {serviceData.map((service) => (
            <div key={service.name} className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: service.color }}
              />
              <div className="flex-1">
                <div className="text-sm font-medium">{service.name}</div>
                <div className="text-xs text-muted-foreground">
                  ${service.revenue.toLocaleString()}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function PerformanceChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Daily Performance</CardTitle>
        <CardDescription>Calls, messages, and bookings throughout the day</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px]">
          <LineChart data={performanceData}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="time"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <ChartLegend content={<ChartLegendContent />} />
            <Line
              type="monotone"
              dataKey="calls"
              strokeWidth={2}
              stroke="var(--color-calls)"
              dot={{ fill: 'var(--color-calls)' }}
            />
            <Line
              type="monotone"
              dataKey="messages"
              strokeWidth={2}
              stroke="var(--color-messages)"
              dot={{ fill: 'var(--color-messages)' }}
            />
            <Line
              type="monotone"
              dataKey="bookings"
              strokeWidth={2}
              stroke="var(--color-bookings)"
              dot={{ fill: 'var(--color-bookings)' }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

export function JobsVsCustomersChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Jobs vs Customers</CardTitle>
        <CardDescription>Monthly comparison of jobs completed and new customers</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px]">
          <BarChart data={revenueData}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="jobs"
              fill="var(--color-jobs)"
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="customers"
              fill="var(--color-customers)"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

export function AdvancedChartsGrid() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Comprehensive insights into your business performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Date Range
          </Button>
        </div>
      </div>

      {/* Charts Grid */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <RevenueChart />
            <ServiceDistributionChart />
          </div>
          <div className="grid gap-4 md:grid-cols-2">
            <PerformanceChart />
            <JobsVsCustomersChart />
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <RevenueChart />
          <JobsVsCustomersChart />
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <PerformanceChart />
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <ServiceDistributionChart />
        </TabsContent>
      </Tabs>
    </div>
  );
}
