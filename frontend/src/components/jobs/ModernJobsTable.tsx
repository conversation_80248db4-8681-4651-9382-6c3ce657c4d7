'use client';

import * as React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import {
  Calendar,
  Clock,
  DollarSign,
  MapPin,
  Phone,
  User,
  Wrench,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  PlayCircle,
} from 'lucide-react';

import { DataTable, SelectColumn, ActionsColumn, SortableHeader } from '@/components/ui/data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

// Job data type
export interface Job {
  id: string;
  customer: {
    name: string;
    email: string;
    phone: string;
    avatar?: string;
  };
  service: {
    type: string;
    description: string;
    category: 'plumbing' | 'hvac' | 'electrical' | 'roofing' | 'landscaping';
  };
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  technician?: {
    name: string;
    avatar?: string;
  };
  scheduledDate: Date;
  estimatedDuration: number; // in minutes
  amount: number;
  address: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Mock data
const mockJobs: Job[] = [
  {
    id: 'JOB-001',
    customer: {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: '/avatars/john.jpg',
    },
    service: {
      type: 'Plumbing Repair',
      description: 'Kitchen sink leak repair',
      category: 'plumbing',
    },
    status: 'completed',
    priority: 'medium',
    technician: {
      name: 'Mike Johnson',
      avatar: '/avatars/mike.jpg',
    },
    scheduledDate: new Date('2024-01-15T14:00:00'),
    estimatedDuration: 120,
    amount: 285.00,
    address: '123 Main St, Anytown, ST 12345',
    createdAt: new Date('2024-01-14T10:30:00'),
    updatedAt: new Date('2024-01-15T16:30:00'),
  },
  {
    id: 'JOB-002',
    customer: {
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      phone: '+****************',
    },
    service: {
      type: 'HVAC Maintenance',
      description: 'Annual system inspection and cleaning',
      category: 'hvac',
    },
    status: 'in-progress',
    priority: 'high',
    technician: {
      name: 'David Brown',
    },
    scheduledDate: new Date('2024-01-15T09:00:00'),
    estimatedDuration: 180,
    amount: 450.00,
    address: '456 Oak Ave, Somewhere, ST 67890',
    createdAt: new Date('2024-01-10T15:20:00'),
    updatedAt: new Date('2024-01-15T09:15:00'),
  },
  {
    id: 'JOB-003',
    customer: {
      name: 'Robert Davis',
      email: '<EMAIL>',
      phone: '+****************',
    },
    service: {
      type: 'Electrical Installation',
      description: 'New outlet installation in garage',
      category: 'electrical',
    },
    status: 'scheduled',
    priority: 'low',
    technician: {
      name: 'Lisa Garcia',
    },
    scheduledDate: new Date('2024-01-16T13:00:00'),
    estimatedDuration: 90,
    amount: 750.00,
    address: '789 Pine Rd, Elsewhere, ST 13579',
    createdAt: new Date('2024-01-12T11:45:00'),
    updatedAt: new Date('2024-01-12T11:45:00'),
  },
];

// Status badge component
function StatusBadge({ status }: { status: Job['status'] }) {
  const variants = {
    scheduled: { variant: 'outline' as const, icon: Clock, color: 'text-blue-600' },
    'in-progress': { variant: 'default' as const, icon: PlayCircle, color: 'text-orange-600' },
    completed: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
    cancelled: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
  };

  const config = variants[status];
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className={cn('h-3 w-3', config.color)} />
      {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
    </Badge>
  );
}

// Priority badge component
function PriorityBadge({ priority }: { priority: Job['priority'] }) {
  const variants = {
    low: { variant: 'outline' as const, color: 'text-gray-600' },
    medium: { variant: 'secondary' as const, color: 'text-blue-600' },
    high: { variant: 'default' as const, color: 'text-orange-600' },
    urgent: { variant: 'destructive' as const, color: 'text-red-600' },
  };

  const config = variants[priority];

  return (
    <Badge variant={config.variant} className={cn('text-xs', config.color)}>
      {priority.toUpperCase()}
    </Badge>
  );
}

// Service category icon
function ServiceIcon({ category }: { category: Job['service']['category'] }) {
  const icons = {
    plumbing: '🔧',
    hvac: '❄️',
    electrical: '⚡',
    roofing: '🏠',
    landscaping: '🌿',
  };

  return (
    <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-sm">
      {icons[category]}
    </div>
  );
}

export function ModernJobsTable() {
  const router = useRouter();
  const [jobs, setJobs] = React.useState<Job[]>(mockJobs);

  const columns: ColumnDef<Job>[] = [
    SelectColumn,
    {
      accessorKey: 'id',
      header: ({ column }) => <SortableHeader column={column}>Job ID</SortableHeader>,
      cell: ({ row }) => (
        <div className="font-mono text-sm font-medium">{row.getValue('id')}</div>
      ),
    },
    {
      accessorKey: 'customer',
      header: 'Customer',
      cell: ({ row }) => {
        const customer = row.getValue('customer') as Job['customer'];
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={customer.avatar} alt={customer.name} />
              <AvatarFallback>
                {customer.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{customer.name}</div>
              <div className="text-xs text-muted-foreground">{customer.phone}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'service',
      header: 'Service',
      cell: ({ row }) => {
        const service = row.getValue('service') as Job['service'];
        return (
          <div className="flex items-center space-x-3">
            <ServiceIcon category={service.category} />
            <div>
              <div className="font-medium">{service.type}</div>
              <div className="text-xs text-muted-foreground">{service.description}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => <SortableHeader column={column}>Status</SortableHeader>,
      cell: ({ row }) => <StatusBadge status={row.getValue('status')} />,
    },
    {
      accessorKey: 'priority',
      header: 'Priority',
      cell: ({ row }) => <PriorityBadge priority={row.getValue('priority')} />,
    },
    {
      accessorKey: 'scheduledDate',
      header: ({ column }) => <SortableHeader column={column}>Scheduled</SortableHeader>,
      cell: ({ row }) => {
        const date = row.getValue('scheduledDate') as Date;
        return (
          <div className="text-sm">
            <div className="font-medium">
              {date.toLocaleDateString()}
            </div>
            <div className="text-muted-foreground">
              {date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'technician',
      header: 'Technician',
      cell: ({ row }) => {
        const technician = row.getValue('technician') as Job['technician'];
        if (!technician) return <span className="text-muted-foreground">Unassigned</span>;
        
        return (
          <div className="flex items-center space-x-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={technician.avatar} alt={technician.name} />
              <AvatarFallback className="text-xs">
                {technician.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{technician.name}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'amount',
      header: ({ column }) => <SortableHeader column={column}>Amount</SortableHeader>,
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue('amount'));
        return (
          <div className="font-medium">
            ${amount.toLocaleString()}
          </div>
        );
      },
    },
    {
      ...ActionsColumn,
      cell: ({ row }) => {
        const job = row.original;
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => router.push(`/jobs/${job.id}`)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push(`/jobs/${job.id}/edit`)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Job
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => window.open(`tel:${job.customer.phone}`)}>
                <Phone className="mr-2 h-4 w-4" />
                Call Customer
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push(`/customers/${job.customer.email}`)}>
                <User className="mr-2 h-4 w-4" />
                View Customer
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="mr-2 h-4 w-4" />
                Cancel Job
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const handleRowClick = (job: Job) => {
    router.push(`/jobs/${job.id}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Jobs Management</h1>
          <p className="text-muted-foreground">
            Manage and track all your service jobs
          </p>
        </div>
        <Button 
          onClick={() => router.push('/jobs/new')}
          className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
        >
          <Calendar className="mr-2 h-4 w-4" />
          New Job
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{jobs.length}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <PlayCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {jobs.filter(j => j.status === 'in-progress').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Active jobs today
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {jobs.filter(j => j.status === 'completed').length}
            </div>
            <p className="text-xs text-muted-foreground">
              This week
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${jobs.reduce((sum, job) => sum + job.amount, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Total value
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Jobs Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Jobs</CardTitle>
          <CardDescription>
            A comprehensive list of all service jobs with advanced filtering and sorting
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={jobs}
            searchKey="customer"
            searchPlaceholder="Search customers..."
            onRowClick={handleRowClick}
          />
        </CardContent>
      </Card>
    </div>
  );
}
