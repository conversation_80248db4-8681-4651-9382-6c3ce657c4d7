'use client';

import React, { useState, useEffect } from 'react';
import { useCompany } from '@/contexts/CompanyContext';
import {
  Plus,
  Edit,
  Copy,
  Eye,
  MoreVertical,
  Search,
  Filter,
  Zap,
  Globe,
  Smartphone,
  Code,
  Settings,
  BarChart3,
  Users,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import {
  webFormsApi,
  type WebForm as ApiWebForm,
  type WebFormSubmission,
} from '@/services/webFormsApi';
import { WebForm } from '@/types/forms';
import { FormStorage, type CustomWebForm } from '@/lib/form-storage';
import { FloatingChatButton } from '@/components/forms/ChatPlugin';

const WebFormsPage: React.FC = () => {
  const [webForms, setWebForms] = useState<ApiWebForm[]>([]);
  const [customForms, setCustomForms] = useState<CustomWebForm[]>([]);
  const [formSubmissions, setFormSubmissions] = useState<WebFormSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const { currentCompany } = useCompany();
  const router = useRouter();

  useEffect(() => {
    if (currentCompany) {
      fetchWebForms();
      fetchFormSubmissions();
    }
    // Load custom forms from storage
    setCustomForms(FormStorage.getAll());
  }, [currentCompany]);

  // Refresh custom forms when returning from form builder
  const refreshCustomForms = () => {
    setCustomForms(FormStorage.getAll());
  };

  const fetchWebForms = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      const response = await webFormsApi.getForms(currentCompany.id);
      setWebForms(response.web_forms);
    } catch (error) {
      console.error('Failed to fetch web forms:', error);
      toast.error('Failed to load web forms');
    } finally {
      setLoading(false);
    }
  };

  const fetchFormSubmissions = async () => {
    if (!currentCompany) return;

    try {
      const response = await webFormsApi.getSubmissions(currentCompany.id);
      setFormSubmissions(response.submissions);
    } catch (error) {
      console.error('Failed to fetch form submissions:', error);
    }
  };

  const filteredForms = webForms.filter((form) => {
    const matchesSearch =
      form.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      form.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || form.status === filterStatus;
    const matchesType = filterType === 'all' || form.form_type === filterType;

    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'secondary';
      case 'draft':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'contact':
        return 'blue';
      case 'booking':
        return 'green';
      case 'quote':
        return 'purple';
      case 'survey':
        return 'orange';
      case 'custom':
        return 'gray';
      default:
        return 'gray';
    }
  };

  const handleCreateForm = () => {
    router.push('/web-forms/new');
  };

  const handleCreateServiceRequestForm = () => {
    router.push('/web-forms/service-request');
  };

  const handleFormClick = (formId: number) => {
    router.push(`/forms/${formId}`);
  };

  const handleEditForm = (formId: number, e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    router.push(`/web-forms/${formId}/edit`);
  };

  const handleViewForm = (formId: string) => {
    const form = webForms.find((f) => f.id === formId);
    if (form?.form_url) {
      window.open(form.form_url, '_blank');
    }
  };

  const handleCopyEmbedCode = (formId: string) => {
    const form = webForms.find((f) => f.id === formId);
    if (form?.embed_code) {
      navigator.clipboard.writeText(form.embed_code);
      toast.success('Embed code copied to clipboard!');
    }
  };

  const handleViewAnalytics = (formId: string) => {
    router.push(`/web-forms/${formId}/analytics`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen p-6"
      style={{ background: 'linear-gradient(to br, #ddd6fe, #ffffff, #dcfce7)' }}
    >
      <div className="space-y-6 animate-fade-in">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 animate-slide-down">
          <div>
            <h1
              className="text-3xl font-bold"
              style={{ background: 'var(--gradient-primary)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}
            >
              Web Forms
            </h1>
          <p className="text-muted-foreground">
            Create and manage embeddable forms for your website
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleCreateForm}>
            <Plus className="w-4 h-4 mr-2" />
            Create Form
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card
          className="animate-slide-up stagger-1 backdrop-blur-sm border border-white/20 shadow-2xl"
          style={{ background: 'rgba(255, 255, 255, 0.8)' }}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Forms</p>
                <p className="text-2xl font-bold">{webForms.length + customForms.length}</p>
              </div>
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center"
                style={{ backgroundColor: 'var(--color-primary)', opacity: 0.1 }}
              >
                <Zap className="w-6 h-6" style={{ color: 'var(--color-primary)' }} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-2">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Forms</p>
                <p className="text-2xl font-bold">
                  {webForms.filter((f) => f.status === 'published').length}
                </p>
              </div>
              <Globe className="w-8 h-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-3">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Submissions</p>
                <p className="text-2xl font-bold">
                  {webForms.reduce((sum, f) => sum + f.total_submissions, 0).toLocaleString()}
                </p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-4">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Conversion</p>
                <p className="text-2xl font-bold">
                  {webForms.length > 0
                    ? Math.round(
                        webForms.reduce((sum, f) => sum + f.conversion_rate, 0) / webForms.length,
                      )
                    : 0}
                  %
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="animate-slide-up stagger-5">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search forms..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="contact">Contact</SelectItem>
                <SelectItem value="booking">Booking</SelectItem>
                <SelectItem value="quote">Quote</SelectItem>
                <SelectItem value="survey">Survey</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Forms Table */}
      <Card
        className="animate-slide-up stagger-6 backdrop-blur-sm border border-white/20 shadow-2xl"
        style={{ background: 'rgba(255, 255, 255, 0.8)' }}
      >
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div
              className="w-6 h-6 rounded-full flex items-center justify-center"
              style={{ backgroundColor: 'var(--color-primary)' }}
            >
              <FileText className="w-3 h-3 text-white" />
            </div>
            Web Forms
          </CardTitle>
          <CardDescription>
            Manage your embeddable web forms and track their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Form Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submissions</TableHead>
                <TableHead>Conversion Rate</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredForms.map((form) => (
                <TableRow
                  key={form.id}
                  className="hover:bg-muted/50 cursor-pointer"
                  onClick={() => handleFormClick(form.id)}
                >
                  <TableCell>
                    <div>
                      <div className="font-medium">{form.title}</div>
                      {form.description && (
                        <div className="text-sm text-muted-foreground">{form.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={`border-${getTypeColor(form.form_type)}-200 text-${getTypeColor(form.form_type)}-700`}
                    >
                      {form.form_type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusColor(form.status)}>{form.status}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{form.total_submissions.toLocaleString()}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{form.completion_rate}%</div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{new Date(form.updated_at).toLocaleDateString()}</div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => handleEditForm(form.id, e)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleViewForm(form.id)}>
                          <Eye className="w-4 h-4 mr-2" />
                          Preview
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyEmbedCode(form.id)}>
                          <Code className="w-4 h-4 mr-2" />
                          Copy Embed Code
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleViewAnalytics(form.id)}>
                          <BarChart3 className="w-4 h-4 mr-2" />
                          Analytics
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredForms.length === 0 && (
            <div className="text-center py-12">
              <Zap className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No forms found</h3>
              <p className="text-muted-foreground mb-6">
                {searchQuery || filterStatus !== 'all' || filterType !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Create your first web form to get started'}
              </p>
              {!searchQuery && filterStatus === 'all' && filterType === 'all' && (
                <Button onClick={handleCreateForm}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Form
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Floating Chat Button */}
      <FloatingChatButton
        onBookingComplete={(data) => {
          console.log('Booking completed via chat:', data);
          toast.success('Booking request received! We\'ll contact you shortly.');
          refreshCustomForms();
        }}
        onBookingStart={() => {
          console.log('Chat booking started');
        }}
      />
      </div>
    </div>
  );
};

export default WebFormsPage;
