'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { FormBuilder } from '@/components/forms/FormBuilder';
import { WebFormPageHeader } from '@/components/forms/WebFormPageHeader';
import { fetchWebFormById, saveWebForm } from '@/services/formService';
import { WebForm } from '@/types/forms';
import { ArrowLeft, Eye, Save, Settings } from 'lucide-react';
import { toast } from 'react-hot-toast';

const FormEditPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const formId = params.formId as string;
  const [template, setTemplate] = useState<WebForm | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getForm = async () => {
      try {
        const fetchedTemplate = await fetchWebFormById(formId);
        setTemplate(fetchedTemplate);
      } catch (error) {
        console.error('Failed to fetch form:', error);
        toast.error('Failed to load form.');
      } finally {
        setLoading(false);
      }
    };
    getForm();
  }, [formId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
        <Card className="max-w-md text-center">
          <CardContent className="p-8">
            <h1 className="text-2xl font-bold mb-4">Form Not Found</h1>
            <p className="text-muted-foreground mb-6">
              The form you're looking for doesn't exist or has been deleted.
            </p>
            <Button onClick={() => router.push('/web-forms')}>
              Back to Forms
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleSave = async (updatedTemplate: WebForm) => {
    try {
      await saveWebForm(updatedTemplate);
      toast.success('Form saved successfully!');
    } catch (error) {
      console.error('Error saving form:', error);
      toast.error('Failed to save form. Please try again.');
    }
  };

  const handlePreview = (templateToPreview: WebForm) => {
    router.push(`/web-forms/${formId}/preview`);
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      <WebFormPageHeader
        title={`Edit: ${template.name}`}
        category={template.form_type} // Assuming form_type can be used as category
        mode="edit"
        formId={formId}
        onSave={() => handleSave(template)}
        onPreview={() => handlePreview(template)}
      />

      {/* Form Builder */}
      <FormBuilder
        template={template}
        onSave={handleSave}
        onPreview={handlePreview}
      />
    </div>
  );
};

export default FormEditPage;
