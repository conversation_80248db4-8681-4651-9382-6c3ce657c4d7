'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ModernFormRenderer } from '@/components/forms/ModernFormRenderer';
import { WebFormPageHeader } from '@/components/forms/WebFormPageHeader';
import { fetchWebFormById } from '@/services/formService';
import { WebForm } from '@/types/forms';
import {
  ArrowLeft,
  Edit,
  Settings,
  Eye,
  Share,
  Download,
  Monitor,
  Smartphone,
  Tablet,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';

const FormPreviewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const formId = params.formId as string;

  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [activeTab, setActiveTab] = useState('preview');
  const [template, setTemplate] = useState<WebForm | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getForm = async () => {
      try {
        const fetchedTemplate = await fetchWebFormById(formId);
        setTemplate(fetchedTemplate);
      } catch (error) {
        console.error('Failed to fetch form:', error);
        toast.error('Failed to load form.');
      } finally {
        setLoading(false);
      }
    };
    getForm();
  }, [formId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <Card className="max-w-md text-center">
          <CardContent className="p-8">
            <h1 className="text-2xl font-bold mb-4">Form Not Found</h1>
            <p className="text-muted-foreground mb-6">
              The form you're looking for doesn't exist or has been deleted.
            </p>
            <Button onClick={() => router.push('/web-forms')}>
              Back to Forms
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getDeviceClass = () => {
    switch (previewDevice) {
      case 'mobile':
        return 'max-w-sm mx-auto';
      case 'tablet':
        return 'max-w-2xl mx-auto';
      default:
        return 'w-full';
    }
  };

  const baseUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
  const formUrl = `${baseUrl}/forms/${formId}`;
  const embedCode = `<iframe src="${baseUrl}/embed/${formId}" width="100%" height="600" frameborder="0"></iframe>`;

  return (
    <div className="min-h-screen bg-background text-foreground">
      <WebFormPageHeader
        title={template.name}
        category={template.form_type} // Use form_type as category
        mode="preview"
        formId={formId}
        onEdit={() => router.push(`/web-forms/${formId}/edit`)}
        onShare={() => { /* Implement share logic */ toast.success('Share functionality coming soon!'); }}
        onExport={() => { /* Implement export logic */ toast.success('Export functionality coming soon!'); }}
      />

      {/* Tabs */}
      <div className="container mx-auto px-4 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Settings
            </TabsTrigger>
            <TabsTrigger value="embed" className="flex items-center gap-2">
              <ExternalLink className="w-4 h-4" />
              Embed
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Analytics
            </TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="mt-6">
            <div className="space-y-6">
              {/* Device Selector */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Form Preview</span>
                    <div className="flex items-center gap-2">
                      <Button
                        variant={previewDevice === 'desktop' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setPreviewDevice('desktop')}
                      >
                        <Monitor className="w-4 h-4" />
                      </Button>
                      <Button
                        variant={previewDevice === 'tablet' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setPreviewDevice('tablet')}
                      >
                        <Tablet className="w-4 h-4" />
                      </Button>
                      <Button
                        variant={previewDevice === 'mobile' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setPreviewDevice('mobile')}
                      >
                        <Smartphone className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className={cn("transition-all duration-300", getDeviceClass())}>
                    <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
                      <ModernFormRenderer
                        template={template}
                        onSubmit={(data) => {
                          console.log('Form completed with data:', data);
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Form Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Form Name</label>
                    <p className="text-muted-foreground">{template.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <p className="text-muted-foreground">{template.description}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Category</label>
                    <Badge variant="secondary">{template.form_type}</Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Pages</label>
                    <p className="text-muted-foreground">{template.pages.length} pages</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Form Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Usage Count</label>
                    <p className="text-muted-foreground">{template.submissions_count} times used</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Rating</label>
                    <p className="text-muted-foreground">⭐ {template.conversion_rate}/5.0</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Tags</label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {/* Assuming tags are part of the WebForm or will be added */}
                      {/* {template.tags.map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))} */}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="embed" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Embed This Form</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <label className="text-sm font-medium mb-2 block">Direct Link</label>
                  <div className="flex items-center gap-2">
                    <code className="text-xs bg-muted p-3 rounded flex-1 truncate">
                      {formUrl}
                    </code>
                    <Button size="sm" variant="outline">
                      Copy
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Embed Code</label>
                  <div className="bg-muted p-4 rounded-lg">
                    <code className="text-sm break-all">
                      {embedCode}
                    </code>
                  </div>
                  <Button className="mt-2">
                    Copy Embed Code
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardContent className="p-6">
                  <div className="text-2xl font-bold">0</div>
                  <p className="text-muted-foreground text-sm">Total Views</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="text-2xl font-bold">0</div>
                  <p className="text-muted-foreground text-sm">Submissions</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="text-2xl font-bold">0%</div>
                  <p className="text-muted-foreground text-sm">Conversion Rate</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Analytics data will be displayed here once the form starts receiving submissions.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default FormPreviewPage;
