'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FormBuilder, FormBuilderRef } from '@/components/forms/FormBuilder';
import { TemplateSelector } from '@/components/forms/TemplateSelector';
import { FormTemplate } from '@/types/forms';
import { toast } from 'react-hot-toast';

import { FormBuilderHeader } from '@/components/forms/FormBuilderHeader';
import { useLayout } from '@/contexts/LayoutContext';
import { Button } from '@/components/ui/button'; // Import Button component
import { webFormsApi } from '@/services/webFormsApi';

type ViewMode = 'template-selection' | 'form-builder';

const NewWebFormPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [viewMode, setViewMode] = useState<ViewMode>('template-selection');
  const [selectedTemplate, setSelectedTemplate] = useState<FormTemplate | null>(null);
  const formBuilderRef = useRef<FormBuilderRef>(null);
  const { setHideSidebar } = useLayout();
  const [showPreview, setShowPreview] = useState(false);
  

  useEffect(() => {
    setHideSidebar(true);
    return () => setHideSidebar(false);
  }, [setHideSidebar]);

  

  const handleTemplateSelect = (template: FormTemplate | null) => {
    setSelectedTemplate(template);
    setViewMode('form-builder');
  };

  const handleStartFromScratch = () => {
    setSelectedTemplate(null);
    setViewMode('form-builder');
  };

  const handleSaveForm = async () => {
    if (!formBuilderRef.current) return;

    try {
      const formTemplate = formBuilderRef.current.getCurrentFormTemplate();



      // Create the form using the form builder endpoint
      const response = await webFormsApi.createFormFromBuilder(formTemplate);

      toast.success('Form saved successfully!');

      // Redirect to forms list or edit page
      router.push('/web-forms');

    } catch (error) {
      console.error('Error saving form:', error);
      toast.error('Failed to save form. Please try again.');
    }
  };



  if (viewMode === 'template-selection') {
    return (
      <TemplateSelector
        onTemplateSelect={handleTemplateSelect}
        onStartFromScratch={handleStartFromScratch}
      />
    );
  }

  return (
    <>
      <FormBuilderHeader
        onSave={handleSaveForm}
        showPreview={showPreview}
        togglePreview={setShowPreview}
      />
      <FormBuilder
        ref={formBuilderRef}
        template={selectedTemplate || undefined}
        showPreview={showPreview}
      />
    </>
  );
};
export default NewWebFormPage;
