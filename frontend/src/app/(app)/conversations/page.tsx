'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  MessageSquare,
  Phone,
  Search,
  Filter,
  MoreHorizontal,
  Clock,
  User,
  Calendar,
  Star,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  MessageCircle,
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';

interface Conversation {
  id: number;
  conversation_id: string;
  customer_name?: string;
  customer_phone?: string;
  channel: 'phone' | 'web' | 'sms' | 'chat';
  status: 'active' | 'completed' | 'abandoned' | 'transferred';
  agent_name?: string;
  started_at: string;
  ended_at?: string;
  duration_seconds?: number;
  message_count: number;
  customer_satisfaction?: number;
  outcome?: string;
  summary?: string;
}

const ConversationsPage: React.FC = () => {
  const { currentCompany } = useCompany();
  const router = useRouter();
  const [conversations, setConversations] = useState<Conversation[]>([]);

  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('all');
  const [activeMainTab, setActiveMainTab] = useState('conversations');

  useEffect(() => {
    const fetchConversations = async () => {
      if (!currentCompany) return;

      try {
        // TODO: Replace with actual API call
        // const response = await fetch(`/api/conversations?company_id=${currentCompany.id}`);
        // const data = await response.json();

        // Mock data for now
        const mockData: Conversation[] = [
          {
            id: 1,
            conversation_id: 'conv_001',
            customer_name: 'John Smith',
            customer_phone: '+****************',
            channel: 'phone',
            status: 'completed',
            agent_name: 'AI Assistant Sarah',
            started_at: '2024-01-20T10:30:00Z',
            ended_at: '2024-01-20T10:45:00Z',
            duration_seconds: 900,
            message_count: 12,
            customer_satisfaction: 5,
            outcome: 'appointment_scheduled',
            summary:
              'Customer called about plumbing leak. Scheduled appointment for tomorrow at 2 PM.',
          },
          {
            id: 2,
            conversation_id: 'conv_002',
            customer_name: 'Sarah Johnson',
            customer_phone: '+****************',
            channel: 'web',
            status: 'completed',
            agent_name: 'AI Assistant Mike',
            started_at: '2024-01-20T09:15:00Z',
            ended_at: '2024-01-20T09:25:00Z',
            duration_seconds: 600,
            message_count: 8,
            customer_satisfaction: 4,
            outcome: 'quote_provided',
            summary: 'Customer inquired about HVAC maintenance. Provided quote for annual service.',
          },
          {
            id: 3,
            conversation_id: 'conv_003',
            customer_name: 'Mike Wilson',
            customer_phone: '+****************',
            channel: 'chat',
            status: 'active',
            agent_name: 'AI Assistant Sarah',
            started_at: '2024-01-20T11:00:00Z',
            message_count: 5,
            summary: 'Customer asking about emergency electrical services.',
          },
          {
            id: 4,
            conversation_id: 'conv_004',
            customer_phone: '+****************',
            channel: 'sms',
            status: 'abandoned',
            started_at: '2024-01-20T08:45:00Z',
            message_count: 2,
            summary: 'Customer started inquiry but did not respond.',
          },
        ];

        setConversations(mockData);

        // TODO: Load form submissions
      } catch (error) {
        console.error('Error fetching conversations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConversations();
  }, [currentCompany]);

  const filteredConversations = conversations.filter((conversation) => {
    const matchesSearch =
      conversation.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      conversation.customer_phone?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      conversation.conversation_id.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === 'all') return matchesSearch;
    return matchesSearch && conversation.status === selectedTab;
  });

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'phone':
        return <Phone className="w-4 h-4 text-blue-600" />;
      case 'web':
        return <MessageCircle className="w-4 h-4 text-green-600" />;
      case 'sms':
        return <MessageSquare className="w-4 h-4 text-purple-600" />;
      case 'chat':
        return <MessageSquare className="w-4 h-4 text-orange-600" />;
      default:
        return <MessageSquare className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'abandoned':
        return 'bg-red-100 text-red-800';
      case 'transferred':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderStars = (rating?: number) => {
    if (!rating) return <span className="text-gray-400 text-sm">No rating</span>;

    return (
      <div className="flex items-center space-x-1">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
          />
        ))}
        <span className="text-sm text-gray-600 ml-1">({rating})</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex justify-between items-center animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Conversations
          </h1>
          <p className="text-muted-foreground mt-1">
            Monitor and analyze all customer interactions across channels
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <TrendingUp className="w-4 h-4 mr-2" />
            Analytics
          </Button>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Conversations</p>
                <p className="text-2xl font-bold text-gray-900">{conversations.length}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-100">
                <MessageSquare className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {conversations.filter((c) => c.status === 'completed').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-100">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-gray-900">
                  {conversations.filter((c) => c.status === 'active').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-orange-100">
                <Clock className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Satisfaction</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(
                    conversations
                      .filter((c) => c.customer_satisfaction)
                      .reduce((sum, c) => sum + (c.customer_satisfaction || 0), 0) /
                      conversations.filter((c) => c.customer_satisfaction).length || 0
                  ).toFixed(1)}
                </p>
              </div>
              <div className="p-3 rounded-full bg-yellow-100">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search conversations by customer name, phone, or ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Conversations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Conversations</CardTitle>
          <CardDescription>
            All customer interactions across phone, web, SMS, and chat channels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList>
              <TabsTrigger value="all">All ({conversations.length})</TabsTrigger>
              <TabsTrigger value="active">
                Active ({conversations.filter((c) => c.status === 'active').length})
              </TabsTrigger>
              <TabsTrigger value="completed">
                Completed ({conversations.filter((c) => c.status === 'completed').length})
              </TabsTrigger>
              <TabsTrigger value="abandoned">
                Abandoned ({conversations.filter((c) => c.status === 'abandoned').length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={selectedTab} className="mt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Channel</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Agent</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Messages</TableHead>
                    <TableHead>Satisfaction</TableHead>
                    <TableHead>Started</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredConversations.map((conversation) => (
                    <TableRow
                      key={conversation.id}
                      className="cursor-pointer hover:bg-accent/5 transition-colors"
                      onClick={() => router.push(`/conversations/${conversation.conversation_id}`)}
                    >
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {conversation.customer_name || 'Unknown Customer'}
                          </p>
                          <p className="text-sm text-gray-600">{conversation.customer_phone}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getChannelIcon(conversation.channel)}
                          <span className="capitalize text-sm">{conversation.channel}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(conversation.status)}>
                          {conversation.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{conversation.agent_name || 'Unassigned'}</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {formatDuration(conversation.duration_seconds)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{conversation.message_count}</span>
                      </TableCell>
                      <TableCell>{renderStars(conversation.customer_satisfaction)}</TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-600">
                          {new Date(conversation.started_at).toLocaleString()}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredConversations.length === 0 && (
                <div className="text-center py-8">
                  <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No conversations found</h3>
                  <p className="text-gray-600">
                    {searchTerm
                      ? 'Try adjusting your search terms'
                      : 'Conversations will appear here as customers interact with your AI agents'}
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConversationsPage;
