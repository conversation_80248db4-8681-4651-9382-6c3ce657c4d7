'use client';

import React, { useState, useEffect } from 'react';
import { useCompany } from '@/contexts/CompanyContext';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Phone,
  MessageSquare,
  Calendar,
  DollarSign,
  Users,
  Clock,
  Target,
  Filter,
  Download,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from 'recharts';

const AnalyticsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const { currentCompany } = useCompany();

  useEffect(() => {
    if (currentCompany) {
      fetchAnalytics();
    }
  }, [currentCompany, timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // Simulated analytics data
      const mockData = {
        overview: {
          total_calls: 1247,
          total_calls_change: 12.5,
          answered_calls: 1156,
          answer_rate: 92.7,
          answer_rate_change: 2.3,
          avg_call_duration: 185, // seconds
          avg_call_duration_change: -5.2,
          total_bookings: 342,
          total_bookings_change: 18.7,
          conversion_rate: 29.6,
          conversion_rate_change: 4.1,
          total_revenue: 45680,
          total_revenue_change: 23.4,
        },
        daily_calls: [
          { date: '2024-01-19', calls: 45, answered: 42, bookings: 12 },
          { date: '2024-01-20', calls: 52, answered: 48, bookings: 15 },
          { date: '2024-01-21', calls: 38, answered: 35, bookings: 8 },
          { date: '2024-01-22', calls: 61, answered: 57, bookings: 18 },
          { date: '2024-01-23', calls: 49, answered: 46, bookings: 14 },
          { date: '2024-01-24', calls: 55, answered: 51, bookings: 16 },
          { date: '2024-01-25', calls: 43, answered: 40, bookings: 11 },
        ],
        call_sources: [
          { name: 'Website', value: 45, color: '#3B82F6' },
          { name: 'Google Ads', value: 28, color: '#10B981' },
          { name: 'Referrals', value: 15, color: '#F59E0B' },
          { name: 'Direct', value: 12, color: '#EF4444' },
        ],
        service_types: [
          { name: 'Plumbing', calls: 387, bookings: 112, revenue: 15420 },
          { name: 'Electrical', calls: 298, bookings: 89, revenue: 12680 },
          { name: 'HVAC', calls: 245, bookings: 78, revenue: 11250 },
          { name: 'Handyman', calls: 189, bookings: 45, revenue: 4890 },
          { name: 'Other', calls: 128, bookings: 18, revenue: 1440 },
        ],
        hourly_distribution: [
          { hour: '8AM', calls: 12 },
          { hour: '9AM', calls: 28 },
          { hour: '10AM', calls: 45 },
          { hour: '11AM', calls: 52 },
          { hour: '12PM', calls: 38 },
          { hour: '1PM', calls: 41 },
          { hour: '2PM', calls: 49 },
          { hour: '3PM', calls: 55 },
          { hour: '4PM', calls: 47 },
          { hour: '5PM', calls: 33 },
          { hour: '6PM', calls: 18 },
        ],
      };
      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatChange = (change: number) => {
    const isPositive = change > 0;
    return (
      <div
        className={`flex items-center gap-1 text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}
      >
        {isPositive ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
        {Math.abs(change)}%
      </div>
    );
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="p-4 flex flex-1 items-center justify-center rounded-lg border border-dashed shadow-sm">
        <div className="flex flex-col items-center gap-1 text-center">
          <h3 className="text-2xl font-bold tracking-tight">No analytics data yet</h3>
          <p className="text-sm text-muted-foreground">
            Analytics will be generated once you have sufficient call data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 animate-slide-down">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Analytics
          </h1>
          <p className="text-muted-foreground">
            Comprehensive insights into your business performance
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="animate-slide-up stagger-1">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Calls</p>
                <p className="text-2xl font-bold">
                  {analyticsData.overview.total_calls.toLocaleString()}
                </p>
                {formatChange(analyticsData.overview.total_calls_change)}
              </div>
              <Phone className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-2">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Answer Rate</p>
                <p className="text-2xl font-bold">{analyticsData.overview.answer_rate}%</p>
                {formatChange(analyticsData.overview.answer_rate_change)}
              </div>
              <Target className="w-8 h-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-3">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Bookings</p>
                <p className="text-2xl font-bold">{analyticsData.overview.total_bookings}</p>
                {formatChange(analyticsData.overview.total_bookings_change)}
              </div>
              <Calendar className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="animate-slide-up stagger-4">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                <p className="text-2xl font-bold">
                  ${analyticsData.overview.total_revenue.toLocaleString()}
                </p>
                {formatChange(analyticsData.overview.total_revenue_change)}
              </div>
              <DollarSign className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Daily Calls Chart */}
        <Card className="animate-slide-up stagger-5" variant="elevated">
          <CardHeader>
            <CardTitle>Daily Call Volume</CardTitle>
            <CardDescription>Calls received and answered over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={analyticsData.daily_calls}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                  formatter={(value, name) => [
                    value,
                    name === 'calls'
                      ? 'Total Calls'
                      : name === 'answered'
                        ? 'Answered'
                        : 'Bookings',
                  ]}
                />
                <Area
                  type="monotone"
                  dataKey="calls"
                  stackId="1"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.3}
                />
                <Area
                  type="monotone"
                  dataKey="answered"
                  stackId="2"
                  stroke="#10B981"
                  fill="#10B981"
                  fillOpacity={0.3}
                />
                <Area
                  type="monotone"
                  dataKey="bookings"
                  stackId="3"
                  stroke="#F59E0B"
                  fill="#F59E0B"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Call Sources */}
        <Card className="animate-slide-up stagger-6" variant="elevated">
          <CardHeader>
            <CardTitle>Call Sources</CardTitle>
            <CardDescription>Where your calls are coming from</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analyticsData.call_sources}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {analyticsData.call_sources.map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsPage;
